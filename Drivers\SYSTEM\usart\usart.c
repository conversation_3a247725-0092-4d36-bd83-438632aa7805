/**
 ****************************************************************************************************					 
 * @file        usart.c
 * @version     V1.1
 * @brief       初始化串口，支持printf            
 ****************************************************************************************************
 *
 * V1.1
 * 修改SYS_SUPPORT_OS为真, 则包含的头文件的定义:"os.h"
 *
 ****************************************************************************************************
 */
 
#include "./SYSTEM/sys/sys.h"
#include "./SYSTEM/usart/usart.h"


/* 如果使用os,则包含下面的头文件即可. */
#if SYS_SUPPORT_OS
#include "os.h"   /* os 使用 */
#endif

/* 接收回调函数指针 */
static void (*g_usart_rx_callback)(uint8_t data) = NULL;

/* 注册接收回调函数 */
void usart_register_rx_callback(void (*callback)(uint8_t data))
{
    g_usart_rx_callback = callback;
}

/******************************************************************************************/
/* 加入以下代码, 支持printf函数, 而不需要选择use MicroLIB */

#if 1
#if (__ARMCC_VERSION >= 6010050)            /* 使用AC6编译器时 */
__asm(".global __use_no_semihosting\n\t");  /* 如果使用半主机模式则需要定义__use_no_semihosting */
__asm(".global __ARM_use_no_argv \n\t");    /* AC6编译器需要将main函数定义为不返回类型，否则可能会进入半主机模式 */

#else
/* 使用AC5编译器时, 需要定义__FILE 否则使用半主机模式 */
#pragma import(__use_no_semihosting)

struct __FILE
{
    int handle;
    /* Whatever you require here. If the only file you are using is */
    /* standard output using printf() for debugging, no file handling */
    /* is required. */
};

#endif

/* 如果使用半主机模式则需要定义_ttywrch_sys_exit_sys_command_string,同时兼容AC6和AC5模式 */
int _ttywrch(int ch)
{
    ch = ch;
    return ch;
}

/* 使用_sys_exit()函数则使用半主机模式 */
void _sys_exit(int x)
{
    x = x;
}

char *_sys_command_string(char *cmd, int len)
{
    return NULL;
}

/* FILE 在 stdio.h中定义 */
FILE __stdout;

/* 重定义fputc函数, printf函数返回值通过fputc函数传递 */
int fputc(int ch, FILE *f)
{
    while ((USART_UX->ISR & 0X40) == 0);    /* 等待一个字符传输完成 */

    USART_UX->TDR = (uint8_t)ch;            /* 将要发送的字符 ch 写入TDR寄存器 */
    return ch;
}
#endif

/******************************************************************************************/

#if USART_EN_RX     /* 使用接收中断 */

/* 接收缓冲区, 大小为USART_REC_LEN字节. */
uint8_t g_usart_rx_buf[USART_REC_LEN];

/*  接收状态
 *  bit15      接收完成标志
 *  bit14      接收到的0x0d
 *  bit13~0    接收到的有效字节数目
 */
uint16_t g_usart_rx_sta = 0;

/**
 * @brief       接收中断处理函数
 * @param       
 * @retval      
 */
void USART_UX_IRQHandler(void)
{
    uint8_t rxdata;
#if SYS_SUPPORT_OS  /* 如果SYS_SUPPORT_OS为真，则需要支持OS. */
    OSIntEnter();
#endif

    if (USART_UX->ISR & (1 << 5))               /* 接收到字符 */
    {
        rxdata = USART_UX->RDR;                 /* 获取接收到的字符 */
        
        /* 调用回调函数 */
        if (g_usart_rx_callback != NULL)
        {
            g_usart_rx_callback(rxdata);
        }

        if ((g_usart_rx_sta & 0x8000) == 0)     /* 如果未接收完成? */
        {
            if (g_usart_rx_sta & 0x4000)        /* 如果接收到的字符是0x0d? */
            {
                if (rxdata != 0x0a)             /* 如果接收到的字符是0x0a? (比较接收到的字符是0x0d,如果是0x0a) */
                {
                    g_usart_rx_sta = 0;         /* 接收完成, 重新开始 */
                }
                else
                {
                    g_usart_rx_sta |= 0x8000;   /* 接收到的字符是0x0a,确认接收完成 */
                }
            }
            else                                /* 如果没有接收0x0d */
            {
                if (rxdata == 0x0d)
                {
                    g_usart_rx_sta |= 0x4000;   /* 确认接收0x0d */
                }
                else
                {
                    g_usart_rx_buf[g_usart_rx_sta & 0X3FFF] = rxdata;   /* 存储接收到的数据到 g_usart_rx_buf */
                    g_usart_rx_sta++; 

                    if (g_usart_rx_sta > (USART_REC_LEN - 1))
                    {
                        g_usart_rx_sta = 0;     /* 接收完成, 重新开始 */
                    }
                }
            }
        }
    }

    USART_UX->ICR |= 1 << 3; /* 清除中断标志, 清除中断标志寄存器 */
    
#if SYS_SUPPORT_OS  /* 如果SYS_SUPPORT_OS为真，则需要支持OS. */
    OSIntExit();
#endif
}
#endif

/**
 * @brief       初始化串口
 * @param       sclk: 串口时钟源频率(单位: MHz)
 *              如果使用1 / 6 / 9 / 10 时钟源: rcc_pclk2 = 130Mhz
 *              如果使用2 - 5 / 7 / 8 时钟源: rcc_pclk1 = 130Mhz
 * @note        注意: 必须正确配置sclk, 否则可能会出现串口异常.
 * @param       baudrate: 波特率, 可以计算得到需要的波特率
 * @retval      
 */
void usart_init(uint32_t sclk, uint32_t baudrate)
{
    uint32_t temp;
    /* IO 配置 */
    USART_TX_GPIO_CLK_ENABLE(); /* 使能串口TX时钟 */
    USART_RX_GPIO_CLK_ENABLE(); /* 使能串口RX时钟 */
    USART_UX_CLK_ENABLE();      /* 使能串口时钟 */

    sys_gpio_set(USART_TX_GPIO_PORT, USART_TX_GPIO_PIN,
                 SYS_GPIO_MODE_AF, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_MID, SYS_GPIO_PUPD_PU);   /* 配置TX引脚 模式 */

    sys_gpio_set(USART_RX_GPIO_PORT, USART_RX_GPIO_PIN,
                 SYS_GPIO_MODE_AF, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_MID, SYS_GPIO_PUPD_PU);   /* 配置RX引脚 模式 */

    sys_gpio_af_set(USART_TX_GPIO_PORT, USART_TX_GPIO_PIN, USART_TX_GPIO_AF);   /* TX引脚 使用复用功能, 必须配置正确 */
    sys_gpio_af_set(USART_RX_GPIO_PORT, USART_RX_GPIO_PIN, USART_RX_GPIO_AF);   /* RX引脚 使用复用功能, 必须配置正确 */

    temp = (sclk * 1000000 + baudrate / 2) / baudrate;  /* 得到USARTDIV@OVER8 = 0, 计算得到需要的波特率 */
    /* 配置波特率 */
    USART_UX->BRR = temp;       /* 配置波特率@OVER8 = 0 */
    USART_UX->CR1 = 0;          /* 清除CR1寄存器 */
    USART_UX->CR1 |= 0 << 28;   /* 配置M1 = 0 */
    USART_UX->CR1 |= 0 << 12;   /* 配置M0 = 0 & M1 = 0, 选择8位数据格式 */
    USART_UX->CR1 |= 0 << 15;   /* 配置OVER8 = 0, 16位数据 */
    USART_UX->CR1 |= 1 << 3;    /* 使能串口 */
#if USART_EN_RX  /* 使用接收中断 */
    /* 使用串口中断 */
    USART_UX->CR1 |= 1 << 2;    /* 使用串口中断 */
    USART_UX->CR1 |= 1 << 5;    /* 使用串口接收缓冲区强中断 */
    sys_nvic_init(3, 3, USART_UX_IRQn, 2); /* 抢占优先级2，响应优先级3 */
#endif
    USART_UX->CR1 |= 1 << 0;    /* 使用串口 */
}











