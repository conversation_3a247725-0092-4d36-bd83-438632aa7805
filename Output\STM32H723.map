Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32h723xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32h723xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32h723xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32h723xx.o(RESET) refers to startup_stm32h723xx.o(STACK) for __initial_sp
    startup_stm32h723xx.o(RESET) refers to startup_stm32h723xx.o(.text) for Reset_Handler
    startup_stm32h723xx.o(RESET) refers to main.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32h723xx.o(RESET) refers to usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32h723xx.o(RESET) refers to gps.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32h723xx.o(RESET) refers to esp8266.o(i.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32h723xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32h723xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32h723xx.o(.text) refers to startup_stm32h723xx.o(HEAP) for Heap_Mem
    startup_stm32h723xx.o(.text) refers to startup_stm32h723xx.o(STACK) for Stack_Mem
    main.o(i.HAL_GetTick) refers to main.o(.data) for g_systick_count
    main.o(i.SysTick_Handler) refers to main.o(.data) for g_systick_count
    main.o(i.main) refers to sys.o(i.sys_stm32_clock_init) for sys_stm32_clock_init
    main.o(i.main) refers to main.o(i.systick_init) for systick_init
    main.o(i.main) refers to delay.o(i.delay_init) for delay_init
    main.o(i.main) refers to usart.o(i.usart_init) for usart_init
    main.o(i.main) refers to oled.o(i.oled_init) for oled_init
    main.o(i.main) refers to main.o(i.iwdg_init) for iwdg_init
    main.o(i.main) refers to main.o(i.HAL_GetTick) for HAL_GetTick
    main.o(i.main) refers to main.o(i.state_machine_process) for state_machine_process
    main.o(i.main) refers to main.o(i.iwdg_feed) for iwdg_feed
    main.o(i.main) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.main) refers to main.o(.data) for g_sys_state
    main.o(i.state_change) refers to main.o(i.HAL_GetTick) for HAL_GetTick
    main.o(i.state_change) refers to main.o(.data) for g_last_state_time
    main.o(i.state_machine_process) refers to main.o(i.HAL_GetTick) for HAL_GetTick
    main.o(i.state_machine_process) refers to main.o(i.system_init) for system_init
    main.o(i.state_machine_process) refers to oled.o(i.oled_clear) for oled_clear
    main.o(i.state_machine_process) refers to oled.o(i.oled_set_cursor) for oled_set_cursor
    main.o(i.state_machine_process) refers to oled.o(i.oled_printf) for oled_printf
    main.o(i.state_machine_process) refers to esp8266.o(i.esp8266_get_error) for esp8266_get_error
    main.o(i.state_machine_process) refers to servo.o(i.servo_clamp_open) for servo_clamp_open
    main.o(i.state_machine_process) refers to main.o(i.state_change) for state_change
    main.o(i.state_machine_process) refers to vl53l0x.o(i.vl53l0x_check_clamp) for vl53l0x_check_clamp
    main.o(i.state_machine_process) refers to servo.o(i.servo_clamp_close) for servo_clamp_close
    main.o(i.state_machine_process) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.state_machine_process) refers to motor.o(i.motor_start) for motor_start
    main.o(i.state_machine_process) refers to gps.o(i.gps_update) for gps_update
    main.o(i.state_machine_process) refers to v831_camera.o(i.v831_update) for v831_update
    main.o(i.state_machine_process) refers to esp8266.o(i.esp8266_is_mqtt_connected) for esp8266_is_mqtt_connected
    main.o(i.state_machine_process) refers to esp8266.o(i.esp8266_mqtt_ping) for esp8266_mqtt_ping
    main.o(i.state_machine_process) refers to v831_camera.o(i.v831_get_defect_info) for v831_get_defect_info
    main.o(i.state_machine_process) refers to v831_camera.o(i.v831_is_critical_defect) for v831_is_critical_defect
    main.o(i.state_machine_process) refers to motor.o(i.motor_stop) for motor_stop
    main.o(i.state_machine_process) refers to gps.o(i.gps_get_location) for gps_get_location
    main.o(i.state_machine_process) refers to esp8266.o(i.esp8266_mqtt_publish_defect) for esp8266_mqtt_publish_defect
    main.o(i.state_machine_process) refers to main.o(.data) for g_sys_state
    main.o(i.switch_usart1_device) refers to sys.o(i.sys_gpio_set) for sys_gpio_set
    main.o(i.switch_usart1_device) refers to sys.o(i.sys_gpio_af_set) for sys_gpio_af_set
    main.o(i.switch_usart1_device) refers to usart.o(i.usart_register_rx_callback) for usart_register_rx_callback
    main.o(i.switch_usart1_device) refers to v831_camera.o(i.v831_rx_callback) for v831_rx_callback
    main.o(i.switch_usart1_device) refers to esp8266.o(i.esp8266_rx_callback) for esp8266_rx_callback
    main.o(i.system_init) refers to motor.o(i.motor_init) for motor_init
    main.o(i.system_init) refers to servo.o(i.servo_init) for servo_init
    main.o(i.system_init) refers to vl53l0x.o(i.vl53l0x_init) for vl53l0x_init
    main.o(i.system_init) refers to gps.o(i.gps_init) for gps_init
    main.o(i.system_init) refers to v831_camera.o(i.v831_init) for v831_init
    main.o(i.system_init) refers to esp8266.o(i.esp8266_init) for esp8266_init
    main.o(i.system_init) refers to esp8266.o(i.esp8266_connect_ap) for esp8266_connect_ap
    main.o(i.system_init) refers to esp8266.o(i.esp8266_connect_mqtt) for esp8266_connect_mqtt
    main.o(i.system_init) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.system_init) refers to oled.o(i.oled_init) for oled_init
    v831_camera.o(i.v831_get_defect_info) refers to v831_camera.o(.data) for current_defect
    v831_camera.o(i.v831_init) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    v831_camera.o(i.v831_init) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    v831_camera.o(i.v831_init) refers to _printf_dec.o(.text) for _printf_int_dec
    v831_camera.o(i.v831_init) refers to v831_camera.o(i.v831_uart_init) for v831_uart_init
    v831_camera.o(i.v831_init) refers to rt_memclr.o(.text) for __aeabi_memclr
    v831_camera.o(i.v831_init) refers to delay.o(i.delay_ms) for delay_ms
    v831_camera.o(i.v831_init) refers to v831_camera.o(i.v831_send_command) for v831_send_command
    v831_camera.o(i.v831_init) refers to v831_camera.o(i.v831_wait_response) for v831_wait_response
    v831_camera.o(i.v831_init) refers to __2snprintf.o(.text) for __2snprintf
    v831_camera.o(i.v831_init) refers to v831_camera.o(.bss) for v831_rx_buffer
    v831_camera.o(i.v831_init) refers to v831_camera.o(.data) for v831_rx_count
    v831_camera.o(i.v831_parse_data) refers to strncpy.o(.text) for strncpy
    v831_camera.o(i.v831_parse_data) refers to strstr.o(.text) for strstr
    v831_camera.o(i.v831_parse_data) refers to strtok_r.o(.text) for strtok_r
    v831_camera.o(i.v831_parse_data) refers to strcmpv7m_pel.o(.text) for strcmp
    v831_camera.o(i.v831_parse_data) refers to atoi.o(.text) for atoi
    v831_camera.o(i.v831_parse_data) refers to v831_camera.o(i.v831_init) for v831_init
    v831_camera.o(i.v831_parse_data) refers to v831_camera.o(.data) for v831_rx_count
    v831_camera.o(i.v831_parse_data) refers to v831_camera.o(.bss) for v831_rx_buffer
    v831_camera.o(i.v831_rx_callback) refers to v831_camera.o(.data) for v831_rx_count
    v831_camera.o(i.v831_rx_callback) refers to v831_camera.o(.bss) for v831_rx_buffer
    v831_camera.o(i.v831_uart_init) refers to sys.o(i.sys_gpio_set) for sys_gpio_set
    v831_camera.o(i.v831_uart_init) refers to sys.o(i.sys_gpio_af_set) for sys_gpio_af_set
    v831_camera.o(i.v831_uart_init) refers to usart.o(i.usart_register_rx_callback) for usart_register_rx_callback
    v831_camera.o(i.v831_uart_init) refers to v831_camera.o(i.v831_rx_callback) for v831_rx_callback
    v831_camera.o(i.v831_update) refers to v831_camera.o(i.v831_parse_data) for v831_parse_data
    v831_camera.o(i.v831_update) refers to rt_memclr.o(.text) for __aeabi_memclr
    v831_camera.o(i.v831_update) refers to v831_camera.o(i.v831_init) for v831_init
    v831_camera.o(i.v831_update) refers to v831_camera.o(.data) for v831_rx_count
    v831_camera.o(i.v831_update) refers to v831_camera.o(.bss) for v831_rx_buffer
    v831_camera.o(i.v831_wait_response) refers to main.o(i.HAL_GetTick) for HAL_GetTick
    v831_camera.o(i.v831_wait_response) refers to strstr.o(.text) for strstr
    v831_camera.o(i.v831_wait_response) refers to delay.o(i.delay_ms) for delay_ms
    v831_camera.o(i.v831_wait_response) refers to v831_camera.o(.bss) for v831_rx_buffer
    vl53l0x.o(i.get_system_ms) refers to main.o(i.HAL_GetTick) for HAL_GetTick
    vl53l0x.o(i.i2c_ack) refers to sys.o(i.sys_gpio_pin_set) for sys_gpio_pin_set
    vl53l0x.o(i.i2c_ack) refers to vl53l0x.o(i.i2c_delay) for i2c_delay
    vl53l0x.o(i.i2c_delay) refers to delay.o(i.delay_us) for delay_us
    vl53l0x.o(i.i2c_nack) refers to sys.o(i.sys_gpio_pin_set) for sys_gpio_pin_set
    vl53l0x.o(i.i2c_nack) refers to vl53l0x.o(i.i2c_delay) for i2c_delay
    vl53l0x.o(i.i2c_read_byte) refers to sys.o(i.sys_gpio_set) for sys_gpio_set
    vl53l0x.o(i.i2c_read_byte) refers to sys.o(i.sys_gpio_pin_set) for sys_gpio_pin_set
    vl53l0x.o(i.i2c_read_byte) refers to vl53l0x.o(i.i2c_delay) for i2c_delay
    vl53l0x.o(i.i2c_read_byte) refers to sys.o(i.sys_gpio_pin_get) for sys_gpio_pin_get
    vl53l0x.o(i.i2c_read_byte) refers to vl53l0x.o(i.i2c_ack) for i2c_ack
    vl53l0x.o(i.i2c_read_byte) refers to vl53l0x.o(i.i2c_nack) for i2c_nack
    vl53l0x.o(i.i2c_send_byte) refers to sys.o(i.sys_gpio_pin_set) for sys_gpio_pin_set
    vl53l0x.o(i.i2c_send_byte) refers to vl53l0x.o(i.i2c_delay) for i2c_delay
    vl53l0x.o(i.i2c_start) refers to sys.o(i.sys_gpio_pin_set) for sys_gpio_pin_set
    vl53l0x.o(i.i2c_start) refers to vl53l0x.o(i.i2c_delay) for i2c_delay
    vl53l0x.o(i.i2c_stop) refers to sys.o(i.sys_gpio_pin_set) for sys_gpio_pin_set
    vl53l0x.o(i.i2c_stop) refers to vl53l0x.o(i.i2c_delay) for i2c_delay
    vl53l0x.o(i.i2c_wait_ack) refers to sys.o(i.sys_gpio_set) for sys_gpio_set
    vl53l0x.o(i.i2c_wait_ack) refers to sys.o(i.sys_gpio_pin_set) for sys_gpio_pin_set
    vl53l0x.o(i.i2c_wait_ack) refers to vl53l0x.o(i.i2c_delay) for i2c_delay
    vl53l0x.o(i.i2c_wait_ack) refers to vl53l0x.o(i.i2c_stop) for i2c_stop
    vl53l0x.o(i.i2c_wait_ack) refers to sys.o(i.sys_gpio_pin_get) for sys_gpio_pin_get
    vl53l0x.o(i.update_distance_status) refers to vl53l0x.o(.data) for g_current_distance
    vl53l0x.o(i.vl53l0x_check_clamp) refers to vl53l0x.o(i.vl53l0x_read_distance) for vl53l0x_read_distance
    vl53l0x.o(i.vl53l0x_check_clamp) refers to vl53l0x.o(.data) for g_distance_status
    vl53l0x.o(i.vl53l0x_check_id) refers to vl53l0x.o(i.vl53l0x_read_byte) for vl53l0x_read_byte
    vl53l0x.o(i.vl53l0x_get_status) refers to vl53l0x.o(.data) for g_distance_status
    vl53l0x.o(i.vl53l0x_i2c_init) refers to sys.o(i.sys_gpio_set) for sys_gpio_set
    vl53l0x.o(i.vl53l0x_i2c_init) refers to sys.o(i.sys_gpio_pin_set) for sys_gpio_pin_set
    vl53l0x.o(i.vl53l0x_i2c_init) refers to delay.o(i.delay_ms) for delay_ms
    vl53l0x.o(i.vl53l0x_i2c_init) refers to vl53l0x.o(.data) for g_i2c_initialized
    vl53l0x.o(i.vl53l0x_init) refers to vl53l0x.o(i.vl53l0x_i2c_init) for vl53l0x_i2c_init
    vl53l0x.o(i.vl53l0x_init) refers to delay.o(i.delay_ms) for delay_ms
    vl53l0x.o(i.vl53l0x_init) refers to vl53l0x.o(i.vl53l0x_check_id) for vl53l0x_check_id
    vl53l0x.o(i.vl53l0x_init) refers to vl53l0x.o(i.vl53l0x_read_distance) for vl53l0x_read_distance
    vl53l0x.o(i.vl53l0x_init) refers to vl53l0x.o(.data) for g_current_distance
    vl53l0x.o(i.vl53l0x_poll_ranging) refers to vl53l0x.o(i.vl53l0x_read_byte) for vl53l0x_read_byte
    vl53l0x.o(i.vl53l0x_read_byte) refers to vl53l0x.o(i.i2c_start) for i2c_start
    vl53l0x.o(i.vl53l0x_read_byte) refers to vl53l0x.o(i.i2c_send_byte) for i2c_send_byte
    vl53l0x.o(i.vl53l0x_read_byte) refers to vl53l0x.o(i.i2c_wait_ack) for i2c_wait_ack
    vl53l0x.o(i.vl53l0x_read_byte) refers to vl53l0x.o(i.i2c_stop) for i2c_stop
    vl53l0x.o(i.vl53l0x_read_byte) refers to vl53l0x.o(i.i2c_read_byte) for i2c_read_byte
    vl53l0x.o(i.vl53l0x_read_distance) refers to vl53l0x.o(i.get_system_ms) for get_system_ms
    vl53l0x.o(i.vl53l0x_read_distance) refers to vl53l0x.o(i.vl53l0x_i2c_init) for vl53l0x_i2c_init
    vl53l0x.o(i.vl53l0x_read_distance) refers to vl53l0x.o(i.vl53l0x_start_ranging) for vl53l0x_start_ranging
    vl53l0x.o(i.vl53l0x_read_distance) refers to delay.o(i.delay_ms) for delay_ms
    vl53l0x.o(i.vl53l0x_read_distance) refers to vl53l0x.o(i.update_distance_status) for update_distance_status
    vl53l0x.o(i.vl53l0x_read_distance) refers to vl53l0x.o(i.vl53l0x_poll_ranging) for vl53l0x_poll_ranging
    vl53l0x.o(i.vl53l0x_read_distance) refers to vl53l0x.o(i.vl53l0x_read_range_mm) for vl53l0x_read_range_mm
    vl53l0x.o(i.vl53l0x_read_distance) refers to vl53l0x.o(.data) for last_read_time
    vl53l0x.o(i.vl53l0x_read_multi) refers to vl53l0x.o(i.i2c_start) for i2c_start
    vl53l0x.o(i.vl53l0x_read_multi) refers to vl53l0x.o(i.i2c_send_byte) for i2c_send_byte
    vl53l0x.o(i.vl53l0x_read_multi) refers to vl53l0x.o(i.i2c_wait_ack) for i2c_wait_ack
    vl53l0x.o(i.vl53l0x_read_multi) refers to vl53l0x.o(i.i2c_stop) for i2c_stop
    vl53l0x.o(i.vl53l0x_read_multi) refers to vl53l0x.o(i.i2c_read_byte) for i2c_read_byte
    vl53l0x.o(i.vl53l0x_read_range_mm) refers to vl53l0x.o(i.vl53l0x_read_multi) for vl53l0x_read_multi
    vl53l0x.o(i.vl53l0x_read_range_mm) refers to vl53l0x.o(i.vl53l0x_write_byte) for vl53l0x_write_byte
    vl53l0x.o(i.vl53l0x_start_ranging) refers to vl53l0x.o(i.vl53l0x_write_byte) for vl53l0x_write_byte
    vl53l0x.o(i.vl53l0x_write_byte) refers to vl53l0x.o(i.i2c_start) for i2c_start
    vl53l0x.o(i.vl53l0x_write_byte) refers to vl53l0x.o(i.i2c_send_byte) for i2c_send_byte
    vl53l0x.o(i.vl53l0x_write_byte) refers to vl53l0x.o(i.i2c_wait_ack) for i2c_wait_ack
    vl53l0x.o(i.vl53l0x_write_byte) refers to vl53l0x.o(i.i2c_stop) for i2c_stop
    servo.o(i.servo_clamp_close) refers to servo.o(i.servo_set_position) for servo_set_position
    servo.o(i.servo_clamp_close) refers to servo.o(.data) for g_servo_state
    servo.o(i.servo_clamp_open) refers to servo.o(i.servo_set_position) for servo_set_position
    servo.o(i.servo_clamp_open) refers to servo.o(.data) for g_servo_state
    servo.o(i.servo_generate_pwm) refers to sys.o(i.sys_gpio_pin_set) for sys_gpio_pin_set
    servo.o(i.servo_generate_pwm) refers to delay.o(i.delay_us) for delay_us
    servo.o(i.servo_get_state) refers to servo.o(.data) for g_servo_state
    servo.o(i.servo_init) refers to sys.o(i.sys_gpio_set) for sys_gpio_set
    servo.o(i.servo_init) refers to servo.o(i.servo_clamp_open) for servo_clamp_open
    servo.o(i.servo_init) refers to servo.o(.data) for g_servo_state
    servo.o(i.servo_set_position) refers to servo.o(i.servo_generate_pwm) for servo_generate_pwm
    servo.o(i.servo_set_position) refers to servo.o(.data) for g_servo_state
    oled.o(i.get_font_data) refers to oled.o(.constdata) for font8x16
    oled.o(i.i2c_init) refers to sys.o(i.sys_gpio_set) for sys_gpio_set
    oled.o(i.oled_clear) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    oled.o(i.oled_clear) refers to oled.o(i.oled_write_cmd) for oled_write_cmd
    oled.o(i.oled_clear) refers to oled.o(i.oled_write_data) for oled_write_data
    oled.o(i.oled_display_char) refers to oled.o(i.oled_set_cursor) for oled_set_cursor
    oled.o(i.oled_display_char) refers to oled.o(i.get_font_data) for get_font_data
    oled.o(i.oled_display_char) refers to oled.o(i.oled_draw_point) for oled_draw_point
    oled.o(i.oled_display_image) refers to oled.o(i.oled_write_data) for oled_write_data
    oled.o(i.oled_draw_point) refers to oled.o(i.oled_set_cursor) for oled_set_cursor
    oled.o(i.oled_draw_point) refers to oled.o(i.oled_write_data) for oled_write_data
    oled.o(i.oled_draw_point) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.oled_init) refers to oled.o(i.i2c_init) for i2c_init
    oled.o(i.oled_init) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    oled.o(i.oled_init) refers to oled.o(i.oled_write_cmd) for oled_write_cmd
    oled.o(i.oled_init) refers to oled.o(i.oled_clear) for oled_clear
    oled.o(i.oled_printf) refers to vsnprintf.o(.text) for vsnprintf
    oled.o(i.oled_printf) refers to oled.o(i.oled_display_char) for oled_display_char
    oled.o(i.oled_printf) refers to oled.o(.data) for current_x
    oled.o(i.oled_refresh_gram) refers to oled.o(i.oled_set_cursor) for oled_set_cursor
    oled.o(i.oled_refresh_gram) refers to oled.o(i.oled_write_data) for oled_write_data
    oled.o(i.oled_refresh_gram) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.oled_set_cursor) refers to oled.o(i.oled_write_cmd) for oled_write_cmd
    motor.o(i.motor_get_state) refers to motor.o(.data) for g_motor_state
    motor.o(i.motor_init) refers to sys.o(i.sys_gpio_set) for sys_gpio_set
    motor.o(i.motor_init) refers to motor.o(i.motor_stop) for motor_stop
    motor.o(i.motor_init) refers to motor.o(.data) for g_motor_state
    motor.o(i.motor_start) refers to sys.o(i.sys_gpio_pin_set) for sys_gpio_pin_set
    motor.o(i.motor_start) refers to motor.o(.data) for g_motor_state
    motor.o(i.motor_stop) refers to sys.o(i.sys_gpio_pin_set) for sys_gpio_pin_set
    motor.o(i.motor_stop) refers to motor.o(.data) for g_motor_state
    i2c.o(i.i2c_read) refers to i2c.o(i.i2c_wait_ready) for i2c_wait_ready
    i2c.o(i.i2c_read) refers to i2c.o(i.i2c_wait_event) for i2c_wait_event
    i2c.o(i.i2c_read_multi_reg) refers to i2c.o(i.i2c_write) for i2c_write
    i2c.o(i.i2c_read_multi_reg) refers to i2c.o(i.i2c_read) for i2c_read
    i2c.o(i.i2c_read_reg) refers to i2c.o(i.i2c_write) for i2c_write
    i2c.o(i.i2c_read_reg) refers to i2c.o(i.i2c_read) for i2c_read
    i2c.o(i.i2c_wait_event) refers to i2c.o(i.sys_get_tick_ms) for sys_get_tick_ms
    i2c.o(i.i2c_wait_ready) refers to i2c.o(i.sys_get_tick_ms) for sys_get_tick_ms
    i2c.o(i.i2c_write) refers to i2c.o(i.i2c_wait_ready) for i2c_wait_ready
    i2c.o(i.i2c_write) refers to i2c.o(i.i2c_wait_event) for i2c_wait_event
    i2c.o(i.i2c_write_multi_reg) refers to h1_alloc.o(.text) for malloc
    i2c.o(i.i2c_write_multi_reg) refers to i2c.o(i.i2c_write) for i2c_write
    i2c.o(i.i2c_write_multi_reg) refers to h1_free.o(.text) for free
    i2c.o(i.i2c_write_reg) refers to i2c.o(i.i2c_write) for i2c_write
    i2c.o(i.sys_get_tick_ms) refers to main.o(i.HAL_GetTick) for HAL_GetTick
    gps.o(i.USART2_IRQHandler) refers to gps.o(.data) for gps_rx_count
    gps.o(i.USART2_IRQHandler) refers to gps.o(.bss) for gps_rx_buffer
    gps.o(i.gps_get_location) refers to gps.o(.bss) for current_location
    gps.o(i.gps_init) refers to gps.o(i.gps_uart_init) for gps_uart_init
    gps.o(i.gps_init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    gps.o(i.gps_init) refers to sys.o(i.sys_gpio_pin_set) for sys_gpio_pin_set
    gps.o(i.gps_init) refers to delay.o(i.delay_ms) for delay_ms
    gps.o(i.gps_init) refers to gps.o(.bss) for gps_rx_buffer
    gps.o(i.gps_init) refers to gps.o(.data) for gps_rx_count
    gps.o(i.gps_nmea_to_decimal) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    gps.o(i.gps_parse_data) refers to strstr.o(.text) for strstr
    gps.o(i.gps_parse_data) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    gps.o(i.gps_parse_data) refers to gps.o(i.gps_verify_checksum) for gps_verify_checksum
    gps.o(i.gps_parse_data) refers to strncmp.o(.text) for strncmp
    gps.o(i.gps_parse_data) refers to gps.o(i.gps_parse_rmc) for gps_parse_rmc
    gps.o(i.gps_parse_data) refers to gps.o(i.gps_parse_gga) for gps_parse_gga
    gps.o(i.gps_parse_data) refers to gps.o(.bss) for gps_rx_buffer
    gps.o(i.gps_parse_gga) refers to strncpy.o(.text) for strncpy
    gps.o(i.gps_parse_gga) refers to strtok_r.o(.text) for strtok_r
    gps.o(i.gps_parse_gga) refers to strlen.o(.text) for strlen
    gps.o(i.gps_parse_gga) refers to gps.o(i.gps_nmea_to_decimal) for gps_nmea_to_decimal
    gps.o(i.gps_parse_gga) refers to gps.o(.bss) for current_location
    gps.o(i.gps_parse_rmc) refers to strncpy.o(.text) for strncpy
    gps.o(i.gps_parse_rmc) refers to strtok_r.o(.text) for strtok_r
    gps.o(i.gps_parse_rmc) refers to strlen.o(.text) for strlen
    gps.o(i.gps_parse_rmc) refers to gps.o(i.gps_nmea_to_decimal) for gps_nmea_to_decimal
    gps.o(i.gps_parse_rmc) refers to gps.o(.bss) for current_location
    gps.o(i.gps_uart_init) refers to sys.o(i.sys_gpio_set) for sys_gpio_set
    gps.o(i.gps_uart_init) refers to sys.o(i.sys_gpio_af_set) for sys_gpio_af_set
    gps.o(i.gps_update) refers to gps.o(i.gps_parse_data) for gps_parse_data
    gps.o(i.gps_update) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    gps.o(i.gps_update) refers to gps.o(.data) for gps_rx_count
    gps.o(i.gps_update) refers to gps.o(.bss) for gps_rx_buffer
    gps.o(i.gps_verify_checksum) refers to strlen.o(.text) for strlen
    gps.o(i.gps_verify_checksum) refers to strtol.o(.text) for strtol
    gps.o(i.gps_verify_checksum) refers to gps.o(i.gps_calc_checksum) for gps_calc_checksum
    esp8266.o(i.USART3_IRQHandler) refers to esp8266.o(.data) for esp8266_rx_count
    esp8266.o(i.USART3_IRQHandler) refers to esp8266.o(.bss) for esp8266_rx_buffer
    esp8266.o(i.esp8266_clear_error) refers to esp8266.o(.data) for g_esp8266_error
    esp8266.o(i.esp8266_connect_ap) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp8266.o(i.esp8266_connect_ap) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    esp8266.o(i.esp8266_connect_ap) refers to _printf_str.o(.text) for _printf_str
    esp8266.o(i.esp8266_connect_ap) refers to esp8266.o(i.esp8266_clear_error) for esp8266_clear_error
    esp8266.o(i.esp8266_connect_ap) refers to __2snprintf.o(.text) for __2snprintf
    esp8266.o(i.esp8266_connect_ap) refers to esp8266.o(i.esp8266_send_cmd) for esp8266_send_cmd
    esp8266.o(i.esp8266_connect_ap) refers to delay.o(i.delay_ms) for delay_ms
    esp8266.o(i.esp8266_connect_ap) refers to esp8266.o(.data) for g_retry_count
    esp8266.o(i.esp8266_connect_mqtt) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp8266.o(i.esp8266_connect_mqtt) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    esp8266.o(i.esp8266_connect_mqtt) refers to _printf_str.o(.text) for _printf_str
    esp8266.o(i.esp8266_connect_mqtt) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    esp8266.o(i.esp8266_connect_mqtt) refers to _printf_dec.o(.text) for _printf_int_dec
    esp8266.o(i.esp8266_connect_mqtt) refers to esp8266.o(i.esp8266_clear_error) for esp8266_clear_error
    esp8266.o(i.esp8266_connect_mqtt) refers to esp8266.o(i.esp8266_send_cmd) for esp8266_send_cmd
    esp8266.o(i.esp8266_connect_mqtt) refers to __2snprintf.o(.text) for __2snprintf
    esp8266.o(i.esp8266_connect_mqtt) refers to delay.o(i.delay_ms) for delay_ms
    esp8266.o(i.esp8266_connect_mqtt) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    esp8266.o(i.esp8266_connect_mqtt) refers to strstr.o(.text) for strstr
    esp8266.o(i.esp8266_connect_mqtt) refers to main.o(i.HAL_GetTick) for HAL_GetTick
    esp8266.o(i.esp8266_connect_mqtt) refers to esp8266.o(.data) for g_mqtt_connected
    esp8266.o(i.esp8266_connect_mqtt) refers to esp8266.o(.conststring) for .conststring
    esp8266.o(i.esp8266_connect_mqtt) refers to esp8266.o(.bss) for esp8266_rx_buffer
    esp8266.o(i.esp8266_get_error) refers to esp8266.o(.data) for g_esp8266_error
    esp8266.o(i.esp8266_init) refers to esp8266.o(i.esp8266_uart_init) for esp8266_uart_init
    esp8266.o(i.esp8266_init) refers to delay.o(i.delay_ms) for delay_ms
    esp8266.o(i.esp8266_init) refers to esp8266.o(i.esp8266_clear_error) for esp8266_clear_error
    esp8266.o(i.esp8266_init) refers to esp8266.o(i.esp8266_send_cmd) for esp8266_send_cmd
    esp8266.o(i.esp8266_init) refers to esp8266.o(.data) for g_esp8266_error
    esp8266.o(i.esp8266_is_mqtt_connected) refers to main.o(i.HAL_GetTick) for HAL_GetTick
    esp8266.o(i.esp8266_is_mqtt_connected) refers to esp8266.o(.data) for g_mqtt_connected
    esp8266.o(i.esp8266_mqtt_ping) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp8266.o(i.esp8266_mqtt_ping) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    esp8266.o(i.esp8266_mqtt_ping) refers to _printf_dec.o(.text) for _printf_int_dec
    esp8266.o(i.esp8266_mqtt_ping) refers to __2snprintf.o(.text) for __2snprintf
    esp8266.o(i.esp8266_mqtt_ping) refers to esp8266.o(i.esp8266_send_cmd) for esp8266_send_cmd
    esp8266.o(i.esp8266_mqtt_ping) refers to delay.o(i.delay_ms) for delay_ms
    esp8266.o(i.esp8266_mqtt_ping) refers to strstr.o(.text) for strstr
    esp8266.o(i.esp8266_mqtt_ping) refers to main.o(i.HAL_GetTick) for HAL_GetTick
    esp8266.o(i.esp8266_mqtt_ping) refers to esp8266.o(.bss) for esp8266_rx_buffer
    esp8266.o(i.esp8266_mqtt_ping) refers to esp8266.o(.data) for g_last_ping_time
    esp8266.o(i.esp8266_mqtt_publish) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp8266.o(i.esp8266_mqtt_publish) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    esp8266.o(i.esp8266_mqtt_publish) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    esp8266.o(i.esp8266_mqtt_publish) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    esp8266.o(i.esp8266_mqtt_publish) refers to _printf_dec.o(.text) for _printf_int_dec
    esp8266.o(i.esp8266_mqtt_publish) refers to esp8266.o(i.esp8266_clear_error) for esp8266_clear_error
    esp8266.o(i.esp8266_mqtt_publish) refers to __2snprintf.o(.text) for __2snprintf
    esp8266.o(i.esp8266_mqtt_publish) refers to strlen.o(.text) for strlen
    esp8266.o(i.esp8266_mqtt_publish) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    esp8266.o(i.esp8266_mqtt_publish) refers to esp8266.o(i.esp8266_send_cmd) for esp8266_send_cmd
    esp8266.o(i.esp8266_mqtt_publish) refers to delay.o(i.delay_ms) for delay_ms
    esp8266.o(i.esp8266_mqtt_publish) refers to strstr.o(.text) for strstr
    esp8266.o(i.esp8266_mqtt_publish) refers to esp8266.o(.conststring) for .conststring
    esp8266.o(i.esp8266_mqtt_publish) refers to esp8266.o(.data) for g_retry_count
    esp8266.o(i.esp8266_mqtt_publish) refers to esp8266.o(.bss) for esp8266_rx_buffer
    esp8266.o(i.esp8266_mqtt_publish_defect) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp8266.o(i.esp8266_mqtt_publish_defect) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    esp8266.o(i.esp8266_mqtt_publish_defect) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    esp8266.o(i.esp8266_mqtt_publish_defect) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    esp8266.o(i.esp8266_mqtt_publish_defect) refers to _printf_dec.o(.text) for _printf_int_dec
    esp8266.o(i.esp8266_mqtt_publish_defect) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    esp8266.o(i.esp8266_mqtt_publish_defect) refers to _printf_str.o(.text) for _printf_str
    esp8266.o(i.esp8266_mqtt_publish_defect) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    esp8266.o(i.esp8266_mqtt_publish_defect) refers to esp8266.o(i.esp8266_clear_error) for esp8266_clear_error
    esp8266.o(i.esp8266_mqtt_publish_defect) refers to main.o(i.HAL_GetTick) for HAL_GetTick
    esp8266.o(i.esp8266_mqtt_publish_defect) refers to __2snprintf.o(.text) for __2snprintf
    esp8266.o(i.esp8266_mqtt_publish_defect) refers to strlen.o(.text) for strlen
    esp8266.o(i.esp8266_mqtt_publish_defect) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    esp8266.o(i.esp8266_mqtt_publish_defect) refers to esp8266.o(i.esp8266_send_cmd) for esp8266_send_cmd
    esp8266.o(i.esp8266_mqtt_publish_defect) refers to delay.o(i.delay_ms) for delay_ms
    esp8266.o(i.esp8266_mqtt_publish_defect) refers to strstr.o(.text) for strstr
    esp8266.o(i.esp8266_mqtt_publish_defect) refers to esp8266.o(.constdata) for .constdata
    esp8266.o(i.esp8266_mqtt_publish_defect) refers to esp8266.o(.conststring) for .conststring
    esp8266.o(i.esp8266_mqtt_publish_defect) refers to esp8266.o(.data) for g_retry_count
    esp8266.o(i.esp8266_mqtt_publish_defect) refers to esp8266.o(.bss) for esp8266_rx_buffer
    esp8266.o(i.esp8266_rx_callback) refers to esp8266.o(.data) for esp8266_rx_count
    esp8266.o(i.esp8266_rx_callback) refers to esp8266.o(.bss) for esp8266_rx_buffer
    esp8266.o(i.esp8266_send_at_cmd) refers to strlen.o(.text) for strlen
    esp8266.o(i.esp8266_send_at_cmd) refers to esp8266.o(i.esp8266_send_buffer) for esp8266_send_buffer
    esp8266.o(i.esp8266_send_cmd) refers to rt_memclr.o(.text) for __aeabi_memclr
    esp8266.o(i.esp8266_send_cmd) refers to delay.o(i.delay_ms) for delay_ms
    esp8266.o(i.esp8266_send_cmd) refers to strstr.o(.text) for strstr
    esp8266.o(i.esp8266_send_cmd) refers to esp8266.o(.bss) for esp8266_rx_buffer
    esp8266.o(i.esp8266_send_cmd) refers to esp8266.o(.data) for esp8266_rx_count
    esp8266.o(i.esp8266_send_data) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp8266.o(i.esp8266_send_data) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    esp8266.o(i.esp8266_send_data) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    esp8266.o(i.esp8266_send_data) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    esp8266.o(i.esp8266_send_data) refers to _printf_dec.o(.text) for _printf_int_dec
    esp8266.o(i.esp8266_send_data) refers to esp8266.o(i.esp8266_clear_error) for esp8266_clear_error
    esp8266.o(i.esp8266_send_data) refers to __2snprintf.o(.text) for __2snprintf
    esp8266.o(i.esp8266_send_data) refers to strlen.o(.text) for strlen
    esp8266.o(i.esp8266_send_data) refers to esp8266.o(i.esp8266_send_cmd) for esp8266_send_cmd
    esp8266.o(i.esp8266_send_data) refers to delay.o(i.delay_ms) for delay_ms
    esp8266.o(i.esp8266_send_data) refers to esp8266.o(.data) for g_retry_count
    esp8266.o(i.esp8266_set_max_retries) refers to esp8266.o(.data) for g_max_retries
    esp8266.o(i.esp8266_uart_init) refers to sys.o(i.sys_gpio_set) for sys_gpio_set
    esp8266.o(i.esp8266_uart_init) refers to sys.o(i.sys_gpio_af_set) for sys_gpio_af_set
    esp8266.o(.constdata) refers to esp8266.o(.conststring) for .conststring
    delay.o(i.delay_init) refers to delay.o(.data) for g_fac_us
    delay.o(i.delay_ms) refers to delay.o(i.delay_us) for delay_us
    delay.o(i.delay_us) refers to delay.o(.data) for g_fac_us
    sys.o(i.sys_nvic_init) refers to sys.o(i.sys_nvic_priority_group_config) for sys_nvic_priority_group_config
    sys.o(i.sys_standby) refers to sys.o(i.sys_wfi_set) for sys_wfi_set
    sys.o(i.sys_stm32_clock_init) refers to sys.o(i.sys_clock_set) for sys_clock_set
    sys.o(i.sys_stm32_clock_init) refers to sys.o(i.sys_cache_enable) for sys_cache_enable
    sys.o(i.sys_stm32_clock_init) refers to sys.o(i.sys_nvic_set_vector_table) for sys_nvic_set_vector_table
    usart.o(.rev16_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.revsh_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.rrx_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.USART1_IRQHandler) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.USART1_IRQHandler) refers to usart.o(.data) for g_usart_rx_callback
    usart.o(i.USART1_IRQHandler) refers to usart.o(.bss) for g_usart_rx_buf
    usart.o(i._sys_command_string) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i._sys_exit) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i._ttywrch) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.fputc) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.usart_init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.usart_init) refers to sys.o(i.sys_gpio_set) for sys_gpio_set
    usart.o(i.usart_init) refers to sys.o(i.sys_gpio_af_set) for sys_gpio_af_set
    usart.o(i.usart_init) refers to sys.o(i.sys_nvic_init) for sys_nvic_init
    usart.o(i.usart_register_rx_callback) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.usart_register_rx_callback) refers to usart.o(.data) for g_usart_rx_callback
    usart.o(.bss) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    system_stm32h7xx.o(i.SystemCoreClockUpdate) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    system_stm32h7xx.o(i.SystemCoreClockUpdate) refers to system_stm32h7xx.o(.data) for SystemD2Clock
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    malloc.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    malloc.o(.text) refers (Special) to init_alloc.o(.text) for _init_alloc
    malloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    malloc.o(.text) refers to heapstubs.o(.text) for __Heap_Alloc
    free.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    free.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    free.o(.text) refers to heapstubs.o(.text) for __Heap_Free
    h1_alloc.o(.text) refers (Special) to h1_init.o(.text) for __Heap_Initialize
    h1_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_alloc.o(.text) refers to init_alloc.o(.text) for __Heap_Full
    h1_free.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_alloc_mt.o(.text) refers (Special) to h1_init.o(.text) for __Heap_Initialize
    h1_alloc_mt.o(.text) refers to init_alloc.o(.text) for __Heap_Full
    h1_alloc_mt.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_free_mt.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._FDIterate) refers to heap2.o(.conststring) for .conststring
    heap2.o(i.___Heap_ProvideMemory$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i.___Heap_ProvideMemory$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i.___Heap_Stats$realtime) refers to heap2.o(i._Heap2_StatsIterate) for _Heap2_StatsIterate
    heap2.o(i.___Heap_Valid$realtime) refers to heap2.o(i._FDIterate) for _FDIterate
    heap2.o(i.___Heap_Valid$realtime) refers to heap2.o(.conststring) for .conststring
    heap2.o(i._free$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._free$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i._free$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._malloc$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2.o(i._malloc$realtime) refers to init_alloc.o(.text) for __Heap_Full
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._posix_memalign$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2.o(i._posix_memalign$realtime) refers to init_alloc.o(.text) for __Heap_Full
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._realloc$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._realloc$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._realloc$realtime) refers to h1_free.o(.text) for free
    heap2.o(i._realloc$realtime) refers to h1_alloc.o(.text) for malloc
    heap2.o(i._realloc$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i._realloc$realtime) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    heap2mt.o(i._FDIterate) refers to heap2mt.o(.conststring) for .conststring
    heap2mt.o(i.___Heap_Initialize$realtime$concurrent) refers to mutex_dummy.o(.text) for _mutex_initialize
    heap2mt.o(i.___Heap_ProvideMemory$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i.___Heap_ProvideMemory$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i.___Heap_Stats$realtime$concurrent) refers to heap2mt.o(i._Heap2_StatsIterate) for _Heap2_StatsIterate
    heap2mt.o(i.___Heap_Valid$realtime$concurrent) refers to heap2mt.o(i._FDIterate) for _FDIterate
    heap2mt.o(i.___Heap_Valid$realtime$concurrent) refers to heap2mt.o(.conststring) for .conststring
    heap2mt.o(i._free$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._free$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i._free$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._malloc$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2mt.o(i._malloc$realtime$concurrent) refers to init_alloc.o(.text) for __Heap_Full
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to init_alloc.o(.text) for __Heap_Full
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._realloc$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._realloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._realloc$realtime$concurrent) refers to h1_free.o(.text) for free
    heap2mt.o(i._realloc$realtime$concurrent) refers to h1_alloc.o(.text) for malloc
    heap2mt.o(i._realloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i._realloc$realtime$concurrent) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    vsnprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsnprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsnprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsnprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsnprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsnprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsnprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsnprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsnprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsnprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsnprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsnprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsnprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsnprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsnprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsnprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsnprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsnprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsnprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsnprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsnprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsnprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsnprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsnprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsnprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsnprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsnprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsnprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsnprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsnprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsnprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsnprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsnprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsnprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsnprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsnprintf.o(.text) refers to _sputc.o(.text) for _sputc
    vsnprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __2snprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2snprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __2snprintf.o(.text) refers to _snputc.o(.text) for _snputc
    noretval__2snprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2snprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2snprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    atoi.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    atoi.o(.text) refers to strtol.o(.text) for strtol
    strtol.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    strtol.o(.text) refers to _strtoul.o(.text) for _strtoul
    strtol.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    strtok_r.o(.text) refers to strtok_int.o(.text) for __strtok_internal
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    strncpy.o(.text) refers to rt_memclr.o(.text) for __aeabi_memclr
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    atof.o(i.__hardfp_atof) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atof.o(i.__hardfp_atof) refers to _rserrno.o(.text) for __read_errno
    atof.o(i.__hardfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.__softfp_atof) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atof.o(i.__softfp_atof) refers to _rserrno.o(.text) for __read_errno
    atof.o(i.__softfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.atof) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atof.o(i.atof) refers to _rserrno.o(.text) for __read_errno
    atof.o(i.atof) refers to strtod.o(.text) for __strtod_int
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_heap_descriptor.o(.text) refers to rt_heap_descriptor.o(.bss) for __rt_heap_descriptor_data
    rt_heap_descriptor_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    init_alloc.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    init_alloc.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000005) for __rt_lib_init_heap_2
    init_alloc.o(.text) refers (Special) to maybetermalloc1.o(.emb_text) for _maybe_terminate_alloc
    init_alloc.o(.text) refers to h1_extend.o(.text) for __Heap_ProvideMemory
    init_alloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    init_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    init_alloc.o(.text) refers to h1_init.o(.text) for __Heap_Initialize
    h1_init_mt.o(.text) refers to mutex_dummy.o(.text) for _mutex_initialize
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    _strtoul.o(.text) refers to _chval.o(.text) for _chval
    _strtoul.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    strtod.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    strtod.o(.text) refers to scanf1.o(x$fpl$scanf1) for _scanf_real
    strtod.o(.text) refers to _sgetc.o(.text) for _sgetc
    strtod.o(.text) refers to isspace.o(.text) for isspace
    strtok_int.o(.text) refers to strspn.o(.text) for strspn
    strtok_int.o(.text) refers to strcspn.o(.text) for strcspn
    _get_argv.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv.o(.text) refers to h1_alloc.o(.text) for malloc
    _get_argv.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv.o(.text) refers to usart.o(i._sys_command_string) for _sys_command_string
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000005) refers (Weak) to init_alloc.o(.text) for _init_alloc
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    maybetermalloc2.o(.emb_text) refers (Special) to term_alloc.o(.text) for _terminate_alloc
    h1_extend.o(.text) refers to h1_free.o(.text) for free
    h1_extend_mt.o(.text) refers to h1_free_mt.o(.text) for _free_internal
    isspace.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m_pel.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m_pel.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    heapauxa.o(.text) refers to heapauxa.o(.data) for .data
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf1.o(x$fpl$scanf1) refers to scanf_fp.o(.text) for _scanf_really_real
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32h723xx.o(.text) for __user_initial_stackheap
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    term_alloc.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_heap_2
    term_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    term_alloc.o(.text) refers to h1_final.o(.text) for __Heap_Finalize
    scanf_fp.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf_fp.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    scanf_fp.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    scanf_fp.o(.text) refers to istatus.o(x$fpl$ieeestatus) for __ieee_status
    scanf_fp.o(.text) refers to bigflt0.o(.text) for _btod_etento
    scanf_fp.o(.text) refers to btod.o(CL$$btod_emuld) for _btod_emuld
    scanf_fp.o(.text) refers to btod.o(CL$$btod_edivd) for _btod_edivd
    scanf_fp.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    scanf_fp.o(.text) refers to scanf2.o(x$fpl$scanf2) for _scanf_infnan
    scanf_fp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    scanf_fp.o(.text) refers to fpconst.o(c$$dmax) for __dbl_max
    scanf_fp.o(.text) refers to fpconst.o(c$$dinf) for __huge_val
    scanf_fp.o(.text) refers to narrow.o(i.__mathlib_narrow) for __mathlib_narrow
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_exit.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to usart.o(i._sys_command_string) for _sys_command_string
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to usart.o(i._ttywrch) for _ttywrch
    libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) refers (Weak) to term_alloc.o(.text) for _terminate_alloc
    fpconst.o(c$$dinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$dnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$finf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$dmax) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf2.o(x$fpl$scanf2) refers to scanf_hexfp.o(.text) for _scanf_really_hex_real
    scanf2.o(x$fpl$scanf2) refers to scanf_infnan.o(.text) for _scanf_really_infnan
    scanf2b.o(x$fpl$scanf2) refers to scanf_hexfp.o(.text) for _scanf_really_hex_real
    scanf2b.o(x$fpl$scanf2) refers to scanf_infnan.o(.text) for _scanf_really_infnan
    narrow.o(i.__hardfp___mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__hardfp___mathlib_tofloat) refers to frexp.o(i.frexp) for frexp
    narrow.o(i.__hardfp___mathlib_tofloat) refers to _rserrno.o(.text) for __set_errno
    narrow.o(i.__mathlib_narrow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__mathlib_narrow) refers to narrow.o(i.__hardfp___mathlib_tofloat) for __hardfp___mathlib_tofloat
    narrow.o(i.__mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__mathlib_tofloat) refers to narrow.o(i.__hardfp___mathlib_tofloat) for __hardfp___mathlib_tofloat
    narrow.o(i.__softfp___mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__softfp___mathlib_tofloat) refers to narrow.o(i.__hardfp___mathlib_tofloat) for __hardfp___mathlib_tofloat
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(i._sys_exit) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    scanf_hexfp.o(.text) refers to _chval.o(.text) for _chval
    scanf_hexfp.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    scanf_hexfp.o(.text) refers to ldexp.o(i.__support_ldexp) for __support_ldexp
    scanf_hexfp.o(.text) refers to narrow.o(i.__mathlib_narrow) for __mathlib_narrow
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    frexp.o(i.__hardfp_frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.__softfp_frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    ldexp.o(i.__hardfp_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__hardfp_ldexp) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp.o(i.__hardfp_ldexp) refers to _rserrno.o(.text) for __set_errno
    ldexp.o(i.__hardfp_ldexp) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    ldexp.o(i.__hardfp_ldexp) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    ldexp.o(i.__softfp_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__softfp_ldexp) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp.o(i.__softfp_ldexp) refers to _rserrno.o(.text) for __set_errno
    ldexp.o(i.__softfp_ldexp) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    ldexp.o(i.__softfp_ldexp) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    ldexp.o(i.__support_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__support_ldexp) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp.o(i.__support_ldexp) refers to _rserrno.o(.text) for __set_errno
    ldexp.o(i.__support_ldexp) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    ldexp.o(i.__support_ldexp) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    ldexp.o(i.ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.ldexp) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp.o(i.ldexp) refers to _rserrno.o(.text) for __set_errno
    ldexp.o(i.ldexp) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    ldexp.o(i.ldexp) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to _rserrno.o(.text) for __set_errno
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    ldexp_x.o(i.____softfp_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____softfp_ldexp$lsc) refers to ldexp_x.o(i.____hardfp_ldexp$lsc) for ____hardfp_ldexp$lsc
    ldexp_x.o(i.____support_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____support_ldexp$lsc) refers to ldexp_x.o(i.____hardfp_ldexp$lsc) for ____hardfp_ldexp$lsc
    ldexp_x.o(i.__ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.__ldexp$lsc) refers to ldexp_x.o(i.____hardfp_ldexp$lsc) for ____hardfp_ldexp$lsc
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(i.switch_usart1_device), (160 bytes).
    Removing v831_camera.o(.rev16_text), (4 bytes).
    Removing v831_camera.o(.revsh_text), (4 bytes).
    Removing v831_camera.o(.rrx_text), (6 bytes).
    Removing vl53l0x.o(.rev16_text), (4 bytes).
    Removing vl53l0x.o(.revsh_text), (4 bytes).
    Removing vl53l0x.o(.rrx_text), (6 bytes).
    Removing vl53l0x.o(i.vl53l0x_get_status), (12 bytes).
    Removing servo.o(.rev16_text), (4 bytes).
    Removing servo.o(.revsh_text), (4 bytes).
    Removing servo.o(.rrx_text), (6 bytes).
    Removing servo.o(i.servo_get_state), (12 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(.rrx_text), (6 bytes).
    Removing oled.o(i.oled_display_image), (26 bytes).
    Removing oled.o(i.oled_refresh_gram), (56 bytes).
    Removing motor.o(.rev16_text), (4 bytes).
    Removing motor.o(.revsh_text), (4 bytes).
    Removing motor.o(.rrx_text), (6 bytes).
    Removing motor.o(i.motor_get_state), (12 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing i2c.o(i.i2c_init), (164 bytes).
    Removing i2c.o(i.i2c_read), (118 bytes).
    Removing i2c.o(i.i2c_read_multi_reg), (52 bytes).
    Removing i2c.o(i.i2c_read_reg), (48 bytes).
    Removing i2c.o(i.i2c_wait_event), (60 bytes).
    Removing i2c.o(i.i2c_wait_ready), (40 bytes).
    Removing i2c.o(i.i2c_write), (126 bytes).
    Removing i2c.o(i.i2c_write_multi_reg), (76 bytes).
    Removing i2c.o(i.i2c_write_reg), (32 bytes).
    Removing i2c.o(i.sys_get_tick_ms), (8 bytes).
    Removing gps.o(.rev16_text), (4 bytes).
    Removing gps.o(.revsh_text), (4 bytes).
    Removing gps.o(.rrx_text), (6 bytes).
    Removing esp8266.o(.rev16_text), (4 bytes).
    Removing esp8266.o(.revsh_text), (4 bytes).
    Removing esp8266.o(.rrx_text), (6 bytes).
    Removing esp8266.o(i.esp8266_mqtt_publish), (444 bytes).
    Removing esp8266.o(i.esp8266_rx_callback), (36 bytes).
    Removing esp8266.o(i.esp8266_send_at_cmd), (36 bytes).
    Removing esp8266.o(i.esp8266_send_buffer), (44 bytes).
    Removing esp8266.o(i.esp8266_send_data), (236 bytes).
    Removing esp8266.o(i.esp8266_set_max_retries), (12 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing delay.o(.rrx_text), (6 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.rrx_text), (6 bytes).
    Removing sys.o(i.sys_intx_disable), (4 bytes).
    Removing sys.o(i.sys_intx_enable), (4 bytes).
    Removing sys.o(i.sys_msr_msp), (10 bytes).
    Removing sys.o(i.sys_nvic_ex_config), (268 bytes).
    Removing sys.o(i.sys_soft_reset), (16 bytes).
    Removing sys.o(i.sys_standby), (112 bytes).
    Removing sys.o(i.sys_wfi_set), (4 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i._sys_command_string), (6 bytes).
    Removing usart.o(i._ttywrch), (4 bytes).
    Removing usart.o(i.fputc), (28 bytes).
    Removing system_stm32h7xx.o(.rev16_text), (4 bytes).
    Removing system_stm32h7xx.o(.revsh_text), (4 bytes).
    Removing system_stm32h7xx.o(.rrx_text), (6 bytes).
    Removing system_stm32h7xx.o(i.ExitRun0Mode), (2 bytes).
    Removing system_stm32h7xx.o(i.SystemCoreClockUpdate), (564 bytes).
    Removing system_stm32h7xx.o(i.SystemInit), (268 bytes).
    Removing system_stm32h7xx.o(.constdata), (16 bytes).
    Removing system_stm32h7xx.o(.data), (8 bytes).

76 unused section(s) (total 3306 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_heap_descriptor_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_heap_descriptor.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  mutex_dummy.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/ctype.c                          0x00000000   Number         0  isspace.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_final.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_alloc.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_extend.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_init_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_final_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_alloc_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_init.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_free_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_extend_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_free.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  heap2.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  fdtree.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  heap2mt.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc2.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc2.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  init_alloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc1.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc1.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  free.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  heapstubs.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hguard.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  term_alloc.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxa.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m_pel.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strncpy.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsnprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2snprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2snprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _snputc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _strtoul.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_hexfp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  atoi.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtol.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtod.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_fp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_infnan.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strtok_int.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strcspn.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strncmp.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strstr.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strtok_r.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strspn.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/fpconst.s                       0x00000000   Number         0  fpconst.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/scanf1.s                        0x00000000   Number         0  scanf1.o ABSOLUTE
    ../fplib/scanf2.s                        0x00000000   Number         0  scanf2.o ABSOLUTE
    ../fplib/scanf2a.s                       0x00000000   Number         0  scanf2a.o ABSOLUTE
    ../fplib/scanf2b.s                       0x00000000   Number         0  scanf2b.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/atof.c                        0x00000000   Number         0  atof.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/frexp.c                       0x00000000   Number         0  frexp.o ABSOLUTE
    ../mathlib/ldexp.c                       0x00000000   Number         0  ldexp.o ABSOLUTE
    ../mathlib/ldexp.c                       0x00000000   Number         0  ldexp_x.o ABSOLUTE
    ../mathlib/narrow.c                      0x00000000   Number         0  narrow.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ..\..\Drivers\CMSIS\Device\ST\STM32H7xx\Source\Templates\arm\startup_stm32h723xx.s 0x00000000   Number         0  startup_stm32h723xx.o ABSOLUTE
    ..\..\Drivers\CMSIS\Device\ST\STM32H7xx\Source\Templates\system_stm32h7xx.c 0x00000000   Number         0  system_stm32h7xx.o ABSOLUTE
    ..\..\Drivers\SYSTEM\delay\delay.c       0x00000000   Number         0  delay.o ABSOLUTE
    ..\..\Drivers\SYSTEM\sys\sys.c           0x00000000   Number         0  sys.o ABSOLUTE
    ..\..\Drivers\SYSTEM\usart\usart.c       0x00000000   Number         0  usart.o ABSOLUTE
    ..\..\User\esp8266.c                     0x00000000   Number         0  esp8266.o ABSOLUTE
    ..\..\User\gps.c                         0x00000000   Number         0  gps.o ABSOLUTE
    ..\..\User\i2c.c                         0x00000000   Number         0  i2c.o ABSOLUTE
    ..\..\User\main.c                        0x00000000   Number         0  main.o ABSOLUTE
    ..\..\User\motor.c                       0x00000000   Number         0  motor.o ABSOLUTE
    ..\..\User\oled.c                        0x00000000   Number         0  oled.o ABSOLUTE
    ..\..\User\servo.c                       0x00000000   Number         0  servo.o ABSOLUTE
    ..\..\User\v831_camera.c                 0x00000000   Number         0  v831_camera.o ABSOLUTE
    ..\..\User\vl53l0x.c                     0x00000000   Number         0  vl53l0x.o ABSOLUTE
    ..\\..\\Drivers\\CMSIS\\Device\\ST\\STM32H7xx\\Source\\Templates\\system_stm32h7xx.c 0x00000000   Number         0  system_stm32h7xx.o ABSOLUTE
    ..\\..\\Drivers\\SYSTEM\\delay\\delay.c  0x00000000   Number         0  delay.o ABSOLUTE
    ..\\..\\Drivers\\SYSTEM\\sys\\sys.c      0x00000000   Number         0  sys.o ABSOLUTE
    ..\\..\\Drivers\\SYSTEM\\usart\\usart.c  0x00000000   Number         0  usart.o ABSOLUTE
    ..\\..\\User\\esp8266.c                  0x00000000   Number         0  esp8266.o ABSOLUTE
    ..\\..\\User\\gps.c                      0x00000000   Number         0  gps.o ABSOLUTE
    ..\\..\\User\\i2c.c                      0x00000000   Number         0  i2c.o ABSOLUTE
    ..\\..\\User\\main.c                     0x00000000   Number         0  main.o ABSOLUTE
    ..\\..\\User\\motor.c                    0x00000000   Number         0  motor.o ABSOLUTE
    ..\\..\\User\\oled.c                     0x00000000   Number         0  oled.o ABSOLUTE
    ..\\..\\User\\servo.c                    0x00000000   Number         0  servo.o ABSOLUTE
    ..\\..\\User\\v831_camera.c              0x00000000   Number         0  v831_camera.o ABSOLUTE
    ..\\..\\User\\vl53l0x.c                  0x00000000   Number         0  vl53l0x.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      716  startup_stm32h723xx.o(RESET)
    !!!main                                  0x080002cc   Section        8  __main.o(!!!main)
    !!!scatter                               0x080002d4   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x08000308   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000324   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x08000340   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000001  0x08000340   Section        6  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    .ARM.Collect$$_printf_percent$$00000002  0x08000346   Section        6  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    .ARM.Collect$$_printf_percent$$00000003  0x0800034c   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000004  0x08000352   Section        6  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    .ARM.Collect$$_printf_percent$$00000005  0x08000358   Section        6  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    .ARM.Collect$$_printf_percent$$00000006  0x0800035e   Section        6  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    .ARM.Collect$$_printf_percent$$00000007  0x08000364   Section       10  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    .ARM.Collect$$_printf_percent$$00000008  0x0800036e   Section        6  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    .ARM.Collect$$_printf_percent$$00000009  0x08000374   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x0800037a   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000B  0x08000380   Section        6  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    .ARM.Collect$$_printf_percent$$0000000C  0x08000386   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$0000000D  0x0800038c   Section        6  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    .ARM.Collect$$_printf_percent$$0000000E  0x08000392   Section        6  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    .ARM.Collect$$_printf_percent$$0000000F  0x08000398   Section        6  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    .ARM.Collect$$_printf_percent$$00000010  0x0800039e   Section        6  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    .ARM.Collect$$_printf_percent$$00000011  0x080003a4   Section        6  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    .ARM.Collect$$_printf_percent$$00000012  0x080003aa   Section       10  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    .ARM.Collect$$_printf_percent$$00000013  0x080003b4   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x080003ba   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000015  0x080003c0   Section        6  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    .ARM.Collect$$_printf_percent$$00000016  0x080003c6   Section        6  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    .ARM.Collect$$_printf_percent$$00000017  0x080003cc   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x080003d0   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x080003d2   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x080003d6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080003d6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080003d6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080003d6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x080003d6   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x080003dc   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x080003dc   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$00000013          0x080003e8   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080003e8   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x080003e8   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x080003f2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080003f2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080003f2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080003f2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080003f2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080003f2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080003f2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080003f2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080003f2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080003f2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080003f2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080003f2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080003f2   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080003f4   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080003f6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080003f6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x080003f6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x080003f6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x080003f6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x080003f6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x080003f6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x080003f6   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x080003f8   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080003f8   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080003f8   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080003fe   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080003fe   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000402   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000402   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800040a   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0800040c   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0800040c   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000410   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000418   Section       72  startup_stm32h723xx.o(.text)
    $v0                                      0x08000418   Number         0  startup_stm32h723xx.o(.text)
    .text                                    0x08000460   Section        2  use_no_semi_2.o(.text)
    .text                                    0x08000464   Section        0  vsnprintf.o(.text)
    .text                                    0x08000498   Section        0  __2snprintf.o(.text)
    .text                                    0x080004d0   Section        0  _printf_str.o(.text)
    .text                                    0x08000524   Section        0  _printf_dec.o(.text)
    .text                                    0x0800059c   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x08000724   Section        0  atoi.o(.text)
    .text                                    0x0800073e   Section        0  strtol.o(.text)
    .text                                    0x080007ae   Section        0  strtok_r.o(.text)
    .text                                    0x080007b2   Section        0  strstr.o(.text)
    .text                                    0x080007d6   Section        0  strlen.o(.text)
    .text                                    0x08000814   Section        0  strncmp.o(.text)
    .text                                    0x080008aa   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x08000934   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x08000998   Section       68  rt_memclr.o(.text)
    .text                                    0x080009dc   Section       78  rt_memclr_w.o(.text)
    .text                                    0x08000a2a   Section       86  strncpy.o(.text)
    .text                                    0x08000a80   Section      104  strcmpv7m_pel.o(.text)
    .text                                    0x08000ae8   Section        0  heapauxi.o(.text)
    .text                                    0x08000aee   Section        2  use_no_semi.o(.text)
    .text                                    0x08000af0   Section       16  rt_ctype_table.o(.text)
    .text                                    0x08000b00   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x08000b08   Section        0  _rserrno.o(.text)
    .text                                    0x08000b1e   Section        0  _printf_pad.o(.text)
    .text                                    0x08000b6c   Section        0  _printf_truncate.o(.text)
    .text                                    0x08000b90   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08000c42   Section        0  _printf_charcount.o(.text)
    .text                                    0x08000c6a   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08000c6d   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08001088   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08001089   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x080010b8   Section        0  _sputc.o(.text)
    .text                                    0x080010c2   Section        0  _snputc.o(.text)
    .text                                    0x080010d2   Section        0  _printf_char.o(.text)
    .text                                    0x08001100   Section        0  _printf_wctomb.o(.text)
    .text                                    0x080011bc   Section        0  _printf_longlong_dec.o(.text)
    .text                                    0x08001238   Section        0  _printf_oct_int_ll.o(.text)
    _printf_longlong_oct_internal            0x08001239   Thumb Code     0  _printf_oct_int_ll.o(.text)
    .text                                    0x080012a8   Section        0  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_common                       0x080012a9   Thumb Code     0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x0800133c   Section        0  _strtoul.o(.text)
    .text                                    0x080013dc   Section        0  strtod.o(.text)
    _local_sscanf                            0x080013dd   Thumb Code    60  strtod.o(.text)
    .text                                    0x08001480   Section        0  strtok_int.o(.text)
    .text                                    0x080014c4   Section        8  libspace.o(.text)
    .text                                    0x080014cc   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x080014d4   Section      138  lludiv10.o(.text)
    .text                                    0x0800155e   Section        0  isspace.o(.text)
    .text                                    0x08001570   Section        0  _printf_fp_hex.o(.text)
    .text                                    0x0800186c   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x080018ec   Section        0  _printf_wchar.o(.text)
    .text                                    0x08001918   Section        0  _chval.o(.text)
    .text                                    0x08001934   Section        0  _sgetc.o(.text)
    .text                                    0x08001974   Section        0  bigflt0.o(.text)
    .text                                    0x08001a58   Section        0  _wcrtomb.o(.text)
    .text                                    0x08001a98   Section        0  strcspn.o(.text)
    .text                                    0x08001ab8   Section        0  strspn.o(.text)
    .text                                    0x08001ad4   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08001b20   Section        0  scanf_fp.o(.text)
    _fp_value                                0x08001b21   Thumb Code   588  scanf_fp.o(.text)
    .text                                    0x08002018   Section        0  exit.o(.text)
    .text                                    0x0800202c   Section        0  scanf_hexfp.o(.text)
    .text                                    0x0800234c   Section        0  scanf_infnan.o(.text)
    .text                                    0x08002480   Section       38  llshl.o(.text)
    CL$$btod_d2e                             0x080024a6   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x080024e4   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x0800252a   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x0800258a   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2d                             0x080028c4   Section      132  btod.o(CL$$btod_e2d)
    CL$$btod_e2e                             0x08002948   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08002a24   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_edivd                           0x08002a4e   Section       42  btod.o(CL$$btod_edivd)
    CL$$btod_emul                            0x08002a78   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_emuld                           0x08002aa2   Section       42  btod.o(CL$$btod_emuld)
    CL$$btod_mult_common                     0x08002acc   Section      580  btod.o(CL$$btod_mult_common)
    i.HAL_GetTick                            0x08002d10   Section        0  main.o(i.HAL_GetTick)
    i.SysTick_Handler                        0x08002d1c   Section        0  main.o(i.SysTick_Handler)
    i.USART1_IRQHandler                      0x08002d2c   Section        0  usart.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x08002dd0   Section        0  gps.o(i.USART2_IRQHandler)
    i.USART3_IRQHandler                      0x08002e1c   Section        0  esp8266.o(i.USART3_IRQHandler)
    i.__ARM_fpclassify                       0x08002e58   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__hardfp___mathlib_tofloat             0x08002e88   Section        0  narrow.o(i.__hardfp___mathlib_tofloat)
    i.__hardfp_atof                          0x08002f68   Section        0  atof.o(i.__hardfp_atof)
    i.__mathlib_dbl_overflow                 0x08002fa0   Section        0  dunder.o(i.__mathlib_dbl_overflow)
    i.__mathlib_dbl_underflow                0x08002fb8   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i.__mathlib_narrow                       0x08002fd0   Section        0  narrow.o(i.__mathlib_narrow)
    i.__support_ldexp                        0x08002fe8   Section        0  ldexp.o(i.__support_ldexp)
    i._is_digit                              0x080030a0   Section        0  __printf_wp.o(i._is_digit)
    i._sys_exit                              0x080030ae   Section        0  usart.o(i._sys_exit)
    i.delay_init                             0x080030b4   Section        0  delay.o(i.delay_init)
    i.delay_ms                               0x080030e0   Section        0  delay.o(i.delay_ms)
    i.delay_us                               0x080030f4   Section        0  delay.o(i.delay_us)
    i.esp8266_clear_error                    0x0800313c   Section        0  esp8266.o(i.esp8266_clear_error)
    i.esp8266_connect_ap                     0x08003150   Section        0  esp8266.o(i.esp8266_connect_ap)
    i.esp8266_connect_mqtt                   0x08003204   Section        0  esp8266.o(i.esp8266_connect_mqtt)
    i.esp8266_get_error                      0x08003558   Section        0  esp8266.o(i.esp8266_get_error)
    i.esp8266_init                           0x08003564   Section        0  esp8266.o(i.esp8266_init)
    i.esp8266_is_mqtt_connected              0x080035c8   Section        0  esp8266.o(i.esp8266_is_mqtt_connected)
    i.esp8266_mqtt_ping                      0x08003604   Section        0  esp8266.o(i.esp8266_mqtt_ping)
    i.esp8266_mqtt_publish_defect            0x0800369c   Section        0  esp8266.o(i.esp8266_mqtt_publish_defect)
    i.esp8266_send_cmd                       0x080038c8   Section        0  esp8266.o(i.esp8266_send_cmd)
    esp8266_send_cmd                         0x080038c9   Thumb Code   212  esp8266.o(i.esp8266_send_cmd)
    i.esp8266_uart_init                      0x080039bc   Section        0  esp8266.o(i.esp8266_uart_init)
    esp8266_uart_init                        0x080039bd   Thumb Code   228  esp8266.o(i.esp8266_uart_init)
    i.frexp                                  0x08003ab8   Section        0  frexp.o(i.frexp)
    i.get_font_data                          0x08003b44   Section        0  oled.o(i.get_font_data)
    get_font_data                            0x08003b45   Thumb Code    26  oled.o(i.get_font_data)
    i.get_system_ms                          0x08003b64   Section        0  vl53l0x.o(i.get_system_ms)
    get_system_ms                            0x08003b65   Thumb Code     8  vl53l0x.o(i.get_system_ms)
    i.gps_calc_checksum                      0x08003b6c   Section        0  gps.o(i.gps_calc_checksum)
    gps_calc_checksum                        0x08003b6d   Thumb Code    44  gps.o(i.gps_calc_checksum)
    i.gps_get_location                       0x08003b98   Section        0  gps.o(i.gps_get_location)
    i.gps_init                               0x08003ba8   Section        0  gps.o(i.gps_init)
    i.gps_nmea_to_decimal                    0x08003be0   Section        0  gps.o(i.gps_nmea_to_decimal)
    gps_nmea_to_decimal                      0x08003be1   Thumb Code    88  gps.o(i.gps_nmea_to_decimal)
    i.gps_parse_data                         0x08003c40   Section        0  gps.o(i.gps_parse_data)
    gps_parse_data                           0x08003c41   Thumb Code   130  gps.o(i.gps_parse_data)
    i.gps_parse_gga                          0x08003ce0   Section        0  gps.o(i.gps_parse_gga)
    gps_parse_gga                            0x08003ce1   Thumb Code   210  gps.o(i.gps_parse_gga)
    i.gps_parse_rmc                          0x08003dbc   Section        0  gps.o(i.gps_parse_rmc)
    gps_parse_rmc                            0x08003dbd   Thumb Code   208  gps.o(i.gps_parse_rmc)
    i.gps_uart_init                          0x08003e94   Section        0  gps.o(i.gps_uart_init)
    gps_uart_init                            0x08003e95   Thumb Code   258  gps.o(i.gps_uart_init)
    i.gps_update                             0x08003fac   Section        0  gps.o(i.gps_update)
    i.gps_verify_checksum                    0x08003fd4   Section        0  gps.o(i.gps_verify_checksum)
    gps_verify_checksum                      0x08003fd5   Thumb Code   116  gps.o(i.gps_verify_checksum)
    i.i2c_ack                                0x08004048   Section        0  vl53l0x.o(i.i2c_ack)
    i2c_ack                                  0x08004049   Thumb Code    52  vl53l0x.o(i.i2c_ack)
    i.i2c_delay                              0x08004080   Section        0  vl53l0x.o(i.i2c_delay)
    i2c_delay                                0x08004081   Thumb Code    10  vl53l0x.o(i.i2c_delay)
    i.i2c_init                               0x0800408c   Section        0  oled.o(i.i2c_init)
    i2c_init                                 0x0800408d   Thumb Code    96  oled.o(i.i2c_init)
    i.i2c_nack                               0x080040fc   Section        0  vl53l0x.o(i.i2c_nack)
    i2c_nack                                 0x080040fd   Thumb Code    52  vl53l0x.o(i.i2c_nack)
    i.i2c_read_byte                          0x08004134   Section        0  vl53l0x.o(i.i2c_read_byte)
    i2c_read_byte                            0x08004135   Thumb Code   130  vl53l0x.o(i.i2c_read_byte)
    i.i2c_send_byte                          0x080041bc   Section        0  vl53l0x.o(i.i2c_send_byte)
    i2c_send_byte                            0x080041bd   Thumb Code    88  vl53l0x.o(i.i2c_send_byte)
    i.i2c_start                              0x08004218   Section        0  vl53l0x.o(i.i2c_start)
    i2c_start                                0x08004219   Thumb Code    56  vl53l0x.o(i.i2c_start)
    i.i2c_stop                               0x08004254   Section        0  vl53l0x.o(i.i2c_stop)
    i2c_stop                                 0x08004255   Thumb Code    56  vl53l0x.o(i.i2c_stop)
    i.i2c_wait_ack                           0x08004290   Section        0  vl53l0x.o(i.i2c_wait_ack)
    i2c_wait_ack                             0x08004291   Thumb Code   106  vl53l0x.o(i.i2c_wait_ack)
    i.iwdg_feed                              0x08004300   Section        0  main.o(i.iwdg_feed)
    iwdg_feed                                0x08004301   Thumb Code     2  main.o(i.iwdg_feed)
    i.iwdg_init                              0x08004302   Section        0  main.o(i.iwdg_init)
    iwdg_init                                0x08004303   Thumb Code     2  main.o(i.iwdg_init)
    i.main                                   0x08004304   Section        0  main.o(i.main)
    i.motor_init                             0x08004360   Section        0  motor.o(i.motor_init)
    i.motor_start                            0x08004398   Section        0  motor.o(i.motor_start)
    i.motor_stop                             0x080043b4   Section        0  motor.o(i.motor_stop)
    i.oled_clear                             0x080043d4   Section        0  oled.o(i.oled_clear)
    i.oled_display_char                      0x0800440e   Section        0  oled.o(i.oled_display_char)
    i.oled_draw_point                        0x0800447c   Section        0  oled.o(i.oled_draw_point)
    i.oled_init                              0x08004500   Section        0  oled.o(i.oled_init)
    i.oled_printf                            0x0800454c   Section        0  oled.o(i.oled_printf)
    i.oled_set_cursor                        0x080045ec   Section        0  oled.o(i.oled_set_cursor)
    i.oled_write_cmd                         0x08004610   Section        0  oled.o(i.oled_write_cmd)
    oled_write_cmd                           0x08004611   Thumb Code    96  oled.o(i.oled_write_cmd)
    i.oled_write_data                        0x08004678   Section        0  oled.o(i.oled_write_data)
    oled_write_data                          0x08004679   Thumb Code   124  oled.o(i.oled_write_data)
    i.servo_clamp_close                      0x080046f8   Section        0  servo.o(i.servo_clamp_close)
    i.servo_clamp_open                       0x0800470c   Section        0  servo.o(i.servo_clamp_open)
    i.servo_generate_pwm                     0x08004720   Section        0  servo.o(i.servo_generate_pwm)
    i.servo_init                             0x0800475c   Section        0  servo.o(i.servo_init)
    i.servo_set_position                     0x08004798   Section        0  servo.o(i.servo_set_position)
    i.state_change                           0x080047ec   Section        0  main.o(i.state_change)
    state_change                             0x080047ed   Thumb Code    18  main.o(i.state_change)
    i.state_machine_process                  0x08004808   Section        0  main.o(i.state_machine_process)
    state_machine_process                    0x08004809   Thumb Code  1692  main.o(i.state_machine_process)
    i.sys_cache_enable                       0x08004f04   Section        0  sys.o(i.sys_cache_enable)
    i.sys_clock_set                          0x08005044   Section        0  sys.o(i.sys_clock_set)
    i.sys_gpio_af_set                        0x080053c8   Section        0  sys.o(i.sys_gpio_af_set)
    i.sys_gpio_pin_get                       0x08005430   Section        0  sys.o(i.sys_gpio_pin_get)
    i.sys_gpio_pin_set                       0x08005440   Section        0  sys.o(i.sys_gpio_pin_set)
    i.sys_gpio_set                           0x08005458   Section        0  sys.o(i.sys_gpio_set)
    i.sys_nvic_init                          0x08005538   Section        0  sys.o(i.sys_nvic_init)
    i.sys_nvic_priority_group_config         0x080055b0   Section        0  sys.o(i.sys_nvic_priority_group_config)
    sys_nvic_priority_group_config           0x080055b1   Thumb Code    32  sys.o(i.sys_nvic_priority_group_config)
    i.sys_nvic_set_vector_table              0x080055d8   Section        0  sys.o(i.sys_nvic_set_vector_table)
    i.sys_stm32_clock_init                   0x080055e8   Section        0  sys.o(i.sys_stm32_clock_init)
    i.system_init                            0x08005648   Section        0  main.o(i.system_init)
    system_init                              0x08005649   Thumb Code    60  main.o(i.system_init)
    i.systick_init                           0x08005684   Section        0  main.o(i.systick_init)
    systick_init                             0x08005685   Thumb Code    30  main.o(i.systick_init)
    i.update_distance_status                 0x080056a4   Section        0  vl53l0x.o(i.update_distance_status)
    update_distance_status                   0x080056a5   Thumb Code    50  vl53l0x.o(i.update_distance_status)
    i.usart_init                             0x080056e0   Section        0  usart.o(i.usart_init)
    i.usart_register_rx_callback             0x080057c4   Section        0  usart.o(i.usart_register_rx_callback)
    i.v831_get_defect_info                   0x080057d0   Section        0  v831_camera.o(i.v831_get_defect_info)
    i.v831_init                              0x080057e0   Section        0  v831_camera.o(i.v831_init)
    i.v831_is_critical_defect                0x080058a8   Section        0  v831_camera.o(i.v831_is_critical_defect)
    i.v831_parse_data                        0x080058bc   Section        0  v831_camera.o(i.v831_parse_data)
    v831_parse_data                          0x080058bd   Thumb Code   236  v831_camera.o(i.v831_parse_data)
    i.v831_rx_callback                       0x080059d8   Section        0  v831_camera.o(i.v831_rx_callback)
    i.v831_send_command                      0x08005a1c   Section        0  v831_camera.o(i.v831_send_command)
    v831_send_command                        0x08005a1d   Thumb Code    78  v831_camera.o(i.v831_send_command)
    i.v831_uart_init                         0x08005a70   Section        0  v831_camera.o(i.v831_uart_init)
    v831_uart_init                           0x08005a71   Thumb Code   230  v831_camera.o(i.v831_uart_init)
    i.v831_update                            0x08005b70   Section        0  v831_camera.o(i.v831_update)
    i.v831_wait_response                     0x08005bd0   Section        0  v831_camera.o(i.v831_wait_response)
    v831_wait_response                       0x08005bd1   Thumb Code    48  v831_camera.o(i.v831_wait_response)
    i.vl53l0x_check_clamp                    0x08005c04   Section        0  vl53l0x.o(i.vl53l0x_check_clamp)
    i.vl53l0x_check_id                       0x08005c1c   Section        0  vl53l0x.o(i.vl53l0x_check_id)
    vl53l0x_check_id                         0x08005c1d   Thumb Code    36  vl53l0x.o(i.vl53l0x_check_id)
    i.vl53l0x_i2c_init                       0x08005c40   Section        0  vl53l0x.o(i.vl53l0x_i2c_init)
    vl53l0x_i2c_init                         0x08005c41   Thumb Code    88  vl53l0x.o(i.vl53l0x_i2c_init)
    i.vl53l0x_init                           0x08005ca4   Section        0  vl53l0x.o(i.vl53l0x_init)
    i.vl53l0x_poll_ranging                   0x08005cdc   Section        0  vl53l0x.o(i.vl53l0x_poll_ranging)
    vl53l0x_poll_ranging                     0x08005cdd   Thumb Code    32  vl53l0x.o(i.vl53l0x_poll_ranging)
    i.vl53l0x_read_byte                      0x08005cfc   Section        0  vl53l0x.o(i.vl53l0x_read_byte)
    vl53l0x_read_byte                        0x08005cfd   Thumb Code    90  vl53l0x.o(i.vl53l0x_read_byte)
    i.vl53l0x_read_distance                  0x08005d58   Section        0  vl53l0x.o(i.vl53l0x_read_distance)
    i.vl53l0x_read_multi                     0x08005dc8   Section        0  vl53l0x.o(i.vl53l0x_read_multi)
    vl53l0x_read_multi                       0x08005dc9   Thumb Code   118  vl53l0x.o(i.vl53l0x_read_multi)
    i.vl53l0x_read_range_mm                  0x08005e3e   Section        0  vl53l0x.o(i.vl53l0x_read_range_mm)
    vl53l0x_read_range_mm                    0x08005e3f   Thumb Code    38  vl53l0x.o(i.vl53l0x_read_range_mm)
    i.vl53l0x_start_ranging                  0x08005e64   Section        0  vl53l0x.o(i.vl53l0x_start_ranging)
    vl53l0x_start_ranging                    0x08005e65   Thumb Code    12  vl53l0x.o(i.vl53l0x_start_ranging)
    i.vl53l0x_write_byte                     0x08005e70   Section        0  vl53l0x.o(i.vl53l0x_write_byte)
    vl53l0x_write_byte                       0x08005e71   Thumb Code    78  vl53l0x.o(i.vl53l0x_write_byte)
    locale$$code                             0x08005ec0   Section       44  lc_ctype_c.o(locale$$code)
    locale$$code                             0x08005eec   Section       44  lc_numeric_c.o(locale$$code)
    x$fpl$dcheck1                            0x08005f18   Section       16  dcheck1.o(x$fpl$dcheck1)
    $v0                                      0x08005f18   Number         0  dcheck1.o(x$fpl$dcheck1)
    x$fpl$fpinit                             0x08005f28   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x08005f28   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$ieeestatus                         0x08005f32   Section        6  istatus.o(x$fpl$ieeestatus)
    $v0                                      0x08005f32   Number         0  istatus.o(x$fpl$ieeestatus)
    x$fpl$printf1                            0x08005f38   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x08005f38   Number         0  printf1.o(x$fpl$printf1)
    x$fpl$printf2                            0x08005f3c   Section        4  printf2.o(x$fpl$printf2)
    $v0                                      0x08005f3c   Number         0  printf2.o(x$fpl$printf2)
    x$fpl$retnan                             0x08005f40   Section      100  retnan.o(x$fpl$retnan)
    $v0                                      0x08005f40   Number         0  retnan.o(x$fpl$retnan)
    x$fpl$scalbn                             0x08005fa4   Section       92  scalbn.o(x$fpl$scalbn)
    $v0                                      0x08005fa4   Number         0  scalbn.o(x$fpl$scalbn)
    x$fpl$scanf1                             0x08006000   Section        4  scanf1.o(x$fpl$scanf1)
    $v0                                      0x08006000   Number         0  scanf1.o(x$fpl$scanf1)
    x$fpl$scanf2                             0x08006004   Section        8  scanf2.o(x$fpl$scanf2)
    $v0                                      0x08006004   Number         0  scanf2.o(x$fpl$scanf2)
    x$fpl$trapveneer                         0x0800600c   Section       48  trapv.o(x$fpl$trapveneer)
    $v0                                      0x0800600c   Number         0  trapv.o(x$fpl$trapveneer)
    .constdata                               0x0800603c   Section     1536  oled.o(.constdata)
    x$fpl$usenofp                            0x0800603c   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x0800663c   Section       16  esp8266.o(.constdata)
    .constdata                               0x0800664c   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x0800664c   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x08006660   Section        8  _printf_wctomb.o(.constdata)
    initial_mbstate                          0x08006660   Data           8  _printf_wctomb.o(.constdata)
    .constdata                               0x08006668   Section       40  _printf_hex_int_ll_ptr.o(.constdata)
    uc_hextab                                0x08006668   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    lc_hextab                                0x0800667c   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    .constdata                               0x08006690   Section       38  _printf_fp_hex.o(.constdata)
    lc_hextab                                0x08006690   Data          19  _printf_fp_hex.o(.constdata)
    uc_hextab                                0x080066a3   Data          19  _printf_fp_hex.o(.constdata)
    .constdata                               0x080066b8   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x080066b8   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x080066f4   Data          64  bigflt0.o(.constdata)
    .conststring                             0x0800674c   Section      440  esp8266.o(.conststring)
    c$$dinf                                  0x08006924   Section        8  fpconst.o(c$$dinf)
    c$$dmax                                  0x0800692c   Section        8  fpconst.o(c$$dmax)
    locale$$data                             0x08006934   Section      272  lc_ctype_c.o(locale$$data)
    __lcctype_c_name                         0x08006938   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x08006940   Data           0  lc_ctype_c.o(locale$$data)
    locale$$data                             0x08006a44   Section       28  lc_numeric_c.o(locale$$data)
    __lcctype_c_end                          0x08006a44   Data           0  lc_ctype_c.o(locale$$data)
    __lcnum_c_name                           0x08006a48   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x08006a50   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x08006a5c   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x08006a5e   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x08006a5f   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_end                            0x08006a60   Data           0  lc_numeric_c.o(locale$$data)
    .data                                    0x24000000   Section       28  main.o(.data)
    g_systick_count                          0x24000000   Data           4  main.o(.data)
    g_sys_state                              0x24000004   Data           1  main.o(.data)
    g_last_state_time                        0x24000008   Data           4  main.o(.data)
    g_defect_count                           0x2400000c   Data           4  main.o(.data)
    g_error_count                            0x24000010   Data           1  main.o(.data)
    last_ping_time                           0x24000014   Data           4  main.o(.data)
    last_log_time                            0x24000018   Data           4  main.o(.data)
    .data                                    0x2400001c   Section       11  v831_camera.o(.data)
    v831_rx_count                            0x2400001c   Data           2  v831_camera.o(.data)
    v831_buffer_overflow                     0x2400001e   Data           1  v831_camera.o(.data)
    current_defect                           0x24000020   Data           6  v831_camera.o(.data)
    v831_error_count                         0x24000026   Data           1  v831_camera.o(.data)
    .data                                    0x24000028   Section        8  vl53l0x.o(.data)
    g_current_distance                       0x24000028   Data           2  vl53l0x.o(.data)
    g_distance_status                        0x2400002a   Data           1  vl53l0x.o(.data)
    g_i2c_initialized                        0x2400002b   Data           1  vl53l0x.o(.data)
    last_read_time                           0x2400002c   Data           4  vl53l0x.o(.data)
    .data                                    0x24000030   Section        1  servo.o(.data)
    g_servo_state                            0x24000030   Data           1  servo.o(.data)
    .data                                    0x24000031   Section        2  oled.o(.data)
    current_x                                0x24000031   Data           1  oled.o(.data)
    current_y                                0x24000032   Data           1  oled.o(.data)
    .data                                    0x24000033   Section        1  motor.o(.data)
    g_motor_state                            0x24000033   Data           1  motor.o(.data)
    .data                                    0x24000034   Section        2  gps.o(.data)
    gps_rx_count                             0x24000034   Data           2  gps.o(.data)
    .data                                    0x24000038   Section       12  esp8266.o(.data)
    esp8266_rx_count                         0x24000038   Data           2  esp8266.o(.data)
    g_esp8266_error                          0x2400003a   Data           1  esp8266.o(.data)
    g_retry_count                            0x2400003b   Data           1  esp8266.o(.data)
    g_max_retries                            0x2400003c   Data           1  esp8266.o(.data)
    g_mqtt_connected                         0x2400003d   Data           1  esp8266.o(.data)
    g_last_ping_time                         0x24000040   Data           4  esp8266.o(.data)
    .data                                    0x24000044   Section        4  delay.o(.data)
    g_fac_us                                 0x24000044   Data           4  delay.o(.data)
    .data                                    0x24000048   Section       10  usart.o(.data)
    g_usart_rx_callback                      0x24000048   Data           4  usart.o(.data)
    .bss                                     0x24000054   Section      512  v831_camera.o(.bss)
    v831_rx_buffer                           0x24000054   Data         512  v831_camera.o(.bss)
    .bss                                     0x24000254   Section     1024  oled.o(.bss)
    OLED_GRAM                                0x24000254   Data        1024  oled.o(.bss)
    .bss                                     0x24000654   Section      524  gps.o(.bss)
    gps_rx_buffer                            0x24000654   Data         512  gps.o(.bss)
    current_location                         0x24000854   Data          12  gps.o(.bss)
    .bss                                     0x24000860   Section      512  esp8266.o(.bss)
    esp8266_rx_buffer                        0x24000860   Data         512  esp8266.o(.bss)
    .bss                                     0x24000a60   Section      200  usart.o(.bss)
    .bss                                     0x24000b28   Section       96  libspace.o(.bss)
    HEAP                                     0x24000b88   Section        0  startup_stm32h723xx.o(HEAP)
    STACK                                    0x24000b88   Section     2048  startup_stm32h723xx.o(STACK)
    Heap_Mem                                 0x24000b88   Data           0  startup_stm32h723xx.o(HEAP)
    Stack_Mem                                0x24000b88   Data        2048  startup_stm32h723xx.o(STACK)
    __initial_sp                             0x24001388   Data           0  startup_stm32h723xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPv5_D16$PE$PLD8$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    __user_heap_extent                        - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_free                               - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000002cc   Number         0  startup_stm32h723xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32h723xx.o(RESET)
    __Vectors_End                            0x080002cc   Data           0  startup_stm32h723xx.o(RESET)
    __main                                   0x080002cd   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080002d5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080002d5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080002d5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x080002e3   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x08000309   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000325   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_n                                0x08000341   Thumb Code     0  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    _printf_percent                          0x08000341   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_p                                0x08000347   Thumb Code     0  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    _printf_f                                0x0800034d   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_e                                0x08000353   Thumb Code     0  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    _printf_g                                0x08000359   Thumb Code     0  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    _printf_a                                0x0800035f   Thumb Code     0  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    _printf_ll                               0x08000365   Thumb Code     0  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    _printf_i                                0x0800036f   Thumb Code     0  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    _printf_d                                0x08000375   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x0800037b   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_o                                0x08000381   Thumb Code     0  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    _printf_x                                0x08000387   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_lli                              0x0800038d   Thumb Code     0  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    _printf_lld                              0x08000393   Thumb Code     0  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    _printf_llu                              0x08000399   Thumb Code     0  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    _printf_llo                              0x0800039f   Thumb Code     0  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    _printf_llx                              0x080003a5   Thumb Code     0  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    _printf_l                                0x080003ab   Thumb Code     0  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    _printf_c                                0x080003b5   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x080003bb   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_lc                               0x080003c1   Thumb Code     0  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    _printf_ls                               0x080003c7   Thumb Code     0  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    _printf_percent_end                      0x080003cd   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x080003d1   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x080003d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x080003d7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x080003d7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x080003d7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080003d7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x080003d7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x080003dd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x080003dd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_lc_ctype_1                 0x080003e9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080003e9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x080003e9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x080003f3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080003f3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080003f3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080003f3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080003f3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080003f3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x080003f3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080003f3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x080003f3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080003f3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x080003f3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080003f3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080003f3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x080003f5   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080003f7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x080003f7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080003f7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x080003f7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x080003f7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x080003f7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x080003f7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x080003f7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x080003f9   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080003f9   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080003f9   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080003ff   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080003ff   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000403   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000403   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800040b   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0800040d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0800040d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000411   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000419   Thumb Code    20  startup_stm32h723xx.o(.text)
    NMI_Handler                              0x0800042d   Thumb Code     2  startup_stm32h723xx.o(.text)
    HardFault_Handler                        0x0800042f   Thumb Code     2  startup_stm32h723xx.o(.text)
    MemManage_Handler                        0x08000431   Thumb Code     2  startup_stm32h723xx.o(.text)
    BusFault_Handler                         0x08000433   Thumb Code     2  startup_stm32h723xx.o(.text)
    UsageFault_Handler                       0x08000435   Thumb Code     2  startup_stm32h723xx.o(.text)
    SVC_Handler                              0x08000437   Thumb Code     2  startup_stm32h723xx.o(.text)
    DebugMon_Handler                         0x08000439   Thumb Code     2  startup_stm32h723xx.o(.text)
    PendSV_Handler                           0x0800043b   Thumb Code     2  startup_stm32h723xx.o(.text)
    ADC3_IRQHandler                          0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    ADC_IRQHandler                           0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    BDMA_Channel0_IRQHandler                 0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    BDMA_Channel1_IRQHandler                 0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    BDMA_Channel2_IRQHandler                 0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    BDMA_Channel3_IRQHandler                 0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    BDMA_Channel4_IRQHandler                 0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    BDMA_Channel5_IRQHandler                 0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    BDMA_Channel6_IRQHandler                 0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    BDMA_Channel7_IRQHandler                 0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    CEC_IRQHandler                           0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    COMP1_IRQHandler                         0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    CORDIC_IRQHandler                        0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    CRS_IRQHandler                           0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    DCMI_PSSI_IRQHandler                     0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    DFSDM1_FLT0_IRQHandler                   0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    DFSDM1_FLT1_IRQHandler                   0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    DFSDM1_FLT2_IRQHandler                   0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    DFSDM1_FLT3_IRQHandler                   0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA2D_IRQHandler                         0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMAMUX1_OVR_IRQHandler                   0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMAMUX2_OVR_IRQHandler                   0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    DTS_IRQHandler                           0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    ECC_IRQHandler                           0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    ETH_IRQHandler                           0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    ETH_WKUP_IRQHandler                      0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    EXTI0_IRQHandler                         0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    EXTI15_10_IRQHandler                     0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    EXTI1_IRQHandler                         0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    EXTI2_IRQHandler                         0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    EXTI3_IRQHandler                         0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    EXTI4_IRQHandler                         0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    EXTI9_5_IRQHandler                       0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    FDCAN1_IT0_IRQHandler                    0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    FDCAN1_IT1_IRQHandler                    0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    FDCAN2_IT0_IRQHandler                    0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    FDCAN2_IT1_IRQHandler                    0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    FDCAN3_IT0_IRQHandler                    0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    FDCAN3_IT1_IRQHandler                    0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    FDCAN_CAL_IRQHandler                     0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    FLASH_IRQHandler                         0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    FMAC_IRQHandler                          0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    FMC_IRQHandler                           0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    FPU_IRQHandler                           0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    HSEM1_IRQHandler                         0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    I2C1_ER_IRQHandler                       0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    I2C1_EV_IRQHandler                       0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    I2C2_ER_IRQHandler                       0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    I2C2_EV_IRQHandler                       0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    I2C3_ER_IRQHandler                       0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    I2C3_EV_IRQHandler                       0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    I2C4_ER_IRQHandler                       0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    I2C4_EV_IRQHandler                       0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    I2C5_ER_IRQHandler                       0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    I2C5_EV_IRQHandler                       0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    LPTIM1_IRQHandler                        0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    LPTIM2_IRQHandler                        0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    LPTIM3_IRQHandler                        0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    LPTIM4_IRQHandler                        0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    LPTIM5_IRQHandler                        0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    LPUART1_IRQHandler                       0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    LTDC_ER_IRQHandler                       0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    LTDC_IRQHandler                          0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    MDIOS_IRQHandler                         0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    MDIOS_WKUP_IRQHandler                    0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    MDMA_IRQHandler                          0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    OCTOSPI1_IRQHandler                      0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    OCTOSPI2_IRQHandler                      0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    OTG_HS_IRQHandler                        0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    PVD_AVD_IRQHandler                       0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    RCC_IRQHandler                           0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    RNG_IRQHandler                           0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    RTC_Alarm_IRQHandler                     0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    RTC_WKUP_IRQHandler                      0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    SAI1_IRQHandler                          0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    SAI4_IRQHandler                          0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    SDMMC1_IRQHandler                        0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    SDMMC2_IRQHandler                        0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    SPDIF_RX_IRQHandler                      0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    SPI1_IRQHandler                          0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    SPI2_IRQHandler                          0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    SPI3_IRQHandler                          0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    SPI4_IRQHandler                          0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    SPI5_IRQHandler                          0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    SPI6_IRQHandler                          0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    SWPMI1_IRQHandler                        0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM15_IRQHandler                         0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM16_IRQHandler                         0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM17_IRQHandler                         0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM1_BRK_IRQHandler                      0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM1_CC_IRQHandler                       0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM1_UP_IRQHandler                       0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM23_IRQHandler                         0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM24_IRQHandler                         0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM2_IRQHandler                          0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM3_IRQHandler                          0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM4_IRQHandler                          0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM5_IRQHandler                          0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM6_DAC_IRQHandler                      0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM7_IRQHandler                          0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM8_CC_IRQHandler                       0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    UART4_IRQHandler                         0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    UART5_IRQHandler                         0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    UART7_IRQHandler                         0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    UART8_IRQHandler                         0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    UART9_IRQHandler                         0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    USART10_IRQHandler                       0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    USART6_IRQHandler                        0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    WAKEUP_PIN_IRQHandler                    0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    WWDG_IRQHandler                          0x0800043f   Thumb Code     0  startup_stm32h723xx.o(.text)
    __user_initial_stackheap                 0x08000441   Thumb Code     0  startup_stm32h723xx.o(.text)
    __use_no_semihosting                     0x08000461   Thumb Code     2  use_no_semi_2.o(.text)
    vsnprintf                                0x08000465   Thumb Code    48  vsnprintf.o(.text)
    __2snprintf                              0x08000499   Thumb Code    50  __2snprintf.o(.text)
    _printf_str                              0x080004d1   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x08000525   Thumb Code   104  _printf_dec.o(.text)
    __printf                                 0x0800059d   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    atoi                                     0x08000725   Thumb Code    26  atoi.o(.text)
    strtol                                   0x0800073f   Thumb Code   112  strtol.o(.text)
    _strtok_r                                0x080007af   Thumb Code     4  strtok_r.o(.text)
    strtok_r                                 0x080007af   Thumb Code     0  strtok_r.o(.text)
    strstr                                   0x080007b3   Thumb Code    36  strstr.o(.text)
    strlen                                   0x080007d7   Thumb Code    62  strlen.o(.text)
    strncmp                                  0x08000815   Thumb Code   150  strncmp.o(.text)
    __aeabi_memcpy                           0x080008ab   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x080008ab   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x08000911   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memcpy4                          0x08000935   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x08000935   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x08000935   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x0800097d   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memclr                           0x08000999   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x08000999   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x0800099d   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr4                          0x080009dd   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x080009dd   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x080009dd   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x080009e1   Thumb Code     0  rt_memclr_w.o(.text)
    strncpy                                  0x08000a2b   Thumb Code    86  strncpy.o(.text)
    strcmp                                   0x08000a81   Thumb Code   104  strcmpv7m_pel.o(.text)
    __use_two_region_memory                  0x08000ae9   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow                         0x08000aeb   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand                         0x08000aed   Thumb Code     2  heapauxi.o(.text)
    __semihosting$guard                      0x08000aef   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000aef   Thumb Code     2  use_no_semi.o(.text)
    __rt_ctype_table                         0x08000af1   Thumb Code    16  rt_ctype_table.o(.text)
    __aeabi_errno_addr                       0x08000b01   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x08000b01   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x08000b01   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __read_errno                             0x08000b09   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x08000b13   Thumb Code    12  _rserrno.o(.text)
    _printf_pre_padding                      0x08000b1f   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x08000b4b   Thumb Code    34  _printf_pad.o(.text)
    _printf_truncate_signed                  0x08000b6d   Thumb Code    18  _printf_truncate.o(.text)
    _printf_truncate_unsigned                0x08000b7f   Thumb Code    18  _printf_truncate.o(.text)
    _printf_int_common                       0x08000b91   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_charcount                        0x08000c43   Thumb Code    40  _printf_charcount.o(.text)
    __lib_sel_fp_printf                      0x08000c6b   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x08000e1d   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_char_common                      0x08001093   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x080010b9   Thumb Code    10  _sputc.o(.text)
    _snputc                                  0x080010c3   Thumb Code    16  _snputc.o(.text)
    _printf_cs_common                        0x080010d3   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x080010e7   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x080010f7   Thumb Code     8  _printf_char.o(.text)
    _printf_wctomb                           0x08001101   Thumb Code   182  _printf_wctomb.o(.text)
    _printf_longlong_dec                     0x080011bd   Thumb Code   108  _printf_longlong_dec.o(.text)
    _printf_longlong_oct                     0x08001239   Thumb Code    66  _printf_oct_int_ll.o(.text)
    _printf_int_oct                          0x0800127b   Thumb Code    24  _printf_oct_int_ll.o(.text)
    _printf_ll_oct                           0x08001293   Thumb Code    12  _printf_oct_int_ll.o(.text)
    _printf_longlong_hex                     0x080012a9   Thumb Code    86  _printf_hex_int_ll_ptr.o(.text)
    _printf_int_hex                          0x080012ff   Thumb Code    28  _printf_hex_int_ll_ptr.o(.text)
    _printf_ll_hex                           0x0800131b   Thumb Code    12  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_ptr                          0x08001327   Thumb Code    18  _printf_hex_int_ll_ptr.o(.text)
    _strtoul                                 0x0800133d   Thumb Code   158  _strtoul.o(.text)
    __strtod_int                             0x08001419   Thumb Code    90  strtod.o(.text)
    __strtok_internal                        0x08001481   Thumb Code    64  strtok_int.o(.text)
    __user_libspace                          0x080014c5   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x080014c5   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x080014c5   Thumb Code     0  libspace.o(.text)
    __rt_locale                              0x080014cd   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _ll_udiv10                               0x080014d5   Thumb Code   138  lludiv10.o(.text)
    isspace                                  0x0800155f   Thumb Code    18  isspace.o(.text)
    _printf_fp_hex_real                      0x08001571   Thumb Code   756  _printf_fp_hex.o(.text)
    _printf_fp_infnan                        0x0800186d   Thumb Code   112  _printf_fp_infnan.o(.text)
    _printf_lcs_common                       0x080018ed   Thumb Code    20  _printf_wchar.o(.text)
    _printf_wchar                            0x08001901   Thumb Code    16  _printf_wchar.o(.text)
    _printf_wstring                          0x08001911   Thumb Code     8  _printf_wchar.o(.text)
    _chval                                   0x08001919   Thumb Code    28  _chval.o(.text)
    _sgetc                                   0x08001935   Thumb Code    30  _sgetc.o(.text)
    _sbackspace                              0x08001953   Thumb Code    34  _sgetc.o(.text)
    _btod_etento                             0x08001975   Thumb Code   224  bigflt0.o(.text)
    _wcrtomb                                 0x08001a59   Thumb Code    64  _wcrtomb.o(.text)
    strcspn                                  0x08001a99   Thumb Code    32  strcspn.o(.text)
    strspn                                   0x08001ab9   Thumb Code    28  strspn.o(.text)
    __user_setup_stackheap                   0x08001ad5   Thumb Code    74  sys_stackheap_outer.o(.text)
    _scanf_really_real                       0x08001d6d   Thumb Code   684  scanf_fp.o(.text)
    exit                                     0x08002019   Thumb Code    18  exit.o(.text)
    _scanf_really_hex_real                   0x0800202d   Thumb Code   786  scanf_hexfp.o(.text)
    _scanf_really_infnan                     0x0800234d   Thumb Code   292  scanf_infnan.o(.text)
    __aeabi_llsl                             0x08002481   Thumb Code     0  llshl.o(.text)
    _ll_shift_l                              0x08002481   Thumb Code    38  llshl.o(.text)
    _btod_d2e                                0x080024a7   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x080024e5   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x0800252b   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x0800258b   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2d                                     0x080028c5   Thumb Code   122  btod.o(CL$$btod_e2d)
    _e2e                                     0x08002949   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08002a25   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_edivd                              0x08002a4f   Thumb Code    42  btod.o(CL$$btod_edivd)
    _btod_emul                               0x08002a79   Thumb Code    42  btod.o(CL$$btod_emul)
    _btod_emuld                              0x08002aa3   Thumb Code    42  btod.o(CL$$btod_emuld)
    __btod_mult_common                       0x08002acd   Thumb Code   580  btod.o(CL$$btod_mult_common)
    HAL_GetTick                              0x08002d11   Thumb Code     6  main.o(i.HAL_GetTick)
    SysTick_Handler                          0x08002d1d   Thumb Code    12  main.o(i.SysTick_Handler)
    USART1_IRQHandler                        0x08002d2d   Thumb Code   146  usart.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x08002dd1   Thumb Code    64  gps.o(i.USART2_IRQHandler)
    USART3_IRQHandler                        0x08002e1d   Thumb Code    46  esp8266.o(i.USART3_IRQHandler)
    __ARM_fpclassify                         0x08002e59   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __hardfp___mathlib_tofloat               0x08002e89   Thumb Code   194  narrow.o(i.__hardfp___mathlib_tofloat)
    __hardfp_atof                            0x08002f69   Thumb Code    44  atof.o(i.__hardfp_atof)
    __mathlib_dbl_overflow                   0x08002fa1   Thumb Code    10  dunder.o(i.__mathlib_dbl_overflow)
    __mathlib_dbl_underflow                  0x08002fb9   Thumb Code    10  dunder.o(i.__mathlib_dbl_underflow)
    __mathlib_narrow                         0x08002fd1   Thumb Code    18  narrow.o(i.__mathlib_narrow)
    __support_ldexp                          0x08002fe9   Thumb Code   170  ldexp.o(i.__support_ldexp)
    _is_digit                                0x080030a1   Thumb Code    14  __printf_wp.o(i._is_digit)
    _sys_exit                                0x080030af   Thumb Code     4  usart.o(i._sys_exit)
    delay_init                               0x080030b5   Thumb Code    38  delay.o(i.delay_init)
    delay_ms                                 0x080030e1   Thumb Code    18  delay.o(i.delay_ms)
    delay_us                                 0x080030f5   Thumb Code    68  delay.o(i.delay_us)
    esp8266_clear_error                      0x0800313d   Thumb Code    12  esp8266.o(i.esp8266_clear_error)
    esp8266_connect_ap                       0x08003151   Thumb Code   116  esp8266.o(i.esp8266_connect_ap)
    esp8266_connect_mqtt                     0x08003205   Thumb Code   584  esp8266.o(i.esp8266_connect_mqtt)
    esp8266_get_error                        0x08003559   Thumb Code     6  esp8266.o(i.esp8266_get_error)
    esp8266_init                             0x08003565   Thumb Code    68  esp8266.o(i.esp8266_init)
    esp8266_is_mqtt_connected                0x080035c9   Thumb Code    44  esp8266.o(i.esp8266_is_mqtt_connected)
    esp8266_mqtt_ping                        0x08003605   Thumb Code   106  esp8266.o(i.esp8266_mqtt_ping)
    esp8266_mqtt_publish_defect              0x0800369d   Thumb Code   452  esp8266.o(i.esp8266_mqtt_publish_defect)
    frexp                                    0x08003ab9   Thumb Code   118  frexp.o(i.frexp)
    gps_get_location                         0x08003b99   Thumb Code    10  gps.o(i.gps_get_location)
    gps_init                                 0x08003ba9   Thumb Code    42  gps.o(i.gps_init)
    gps_update                               0x08003fad   Thumb Code    32  gps.o(i.gps_update)
    main                                     0x08004305   Thumb Code    82  main.o(i.main)
    motor_init                               0x08004361   Thumb Code    44  motor.o(i.motor_init)
    motor_start                              0x08004399   Thumb Code    20  motor.o(i.motor_start)
    motor_stop                               0x080043b5   Thumb Code    22  motor.o(i.motor_stop)
    oled_clear                               0x080043d5   Thumb Code    58  oled.o(i.oled_clear)
    oled_display_char                        0x0800440f   Thumb Code   110  oled.o(i.oled_display_char)
    oled_draw_point                          0x0800447d   Thumb Code   126  oled.o(i.oled_draw_point)
    oled_init                                0x08004501   Thumb Code    46  oled.o(i.oled_init)
    oled_printf                              0x0800454d   Thumb Code   152  oled.o(i.oled_printf)
    oled_set_cursor                          0x080045ed   Thumb Code    36  oled.o(i.oled_set_cursor)
    servo_clamp_close                        0x080046f9   Thumb Code    16  servo.o(i.servo_clamp_close)
    servo_clamp_open                         0x0800470d   Thumb Code    16  servo.o(i.servo_clamp_open)
    servo_generate_pwm                       0x08004721   Thumb Code    56  servo.o(i.servo_generate_pwm)
    servo_init                               0x0800475d   Thumb Code    46  servo.o(i.servo_init)
    servo_set_position                       0x08004799   Thumb Code    72  servo.o(i.servo_set_position)
    sys_cache_enable                         0x08004f05   Thumb Code   310  sys.o(i.sys_cache_enable)
    sys_clock_set                            0x08005045   Thumb Code   884  sys.o(i.sys_clock_set)
    sys_gpio_af_set                          0x080053c9   Thumb Code   104  sys.o(i.sys_gpio_af_set)
    sys_gpio_pin_get                         0x08005431   Thumb Code    16  sys.o(i.sys_gpio_pin_get)
    sys_gpio_pin_set                         0x08005441   Thumb Code    24  sys.o(i.sys_gpio_pin_set)
    sys_gpio_set                             0x08005459   Thumb Code   222  sys.o(i.sys_gpio_set)
    sys_nvic_init                            0x08005539   Thumb Code   116  sys.o(i.sys_nvic_init)
    sys_nvic_set_vector_table                0x080055d9   Thumb Code    12  sys.o(i.sys_nvic_set_vector_table)
    sys_stm32_clock_init                     0x080055e9   Thumb Code    88  sys.o(i.sys_stm32_clock_init)
    usart_init                               0x080056e1   Thumb Code   212  usart.o(i.usart_init)
    usart_register_rx_callback               0x080057c5   Thumb Code     6  usart.o(i.usart_register_rx_callback)
    v831_get_defect_info                     0x080057d1   Thumb Code    12  v831_camera.o(i.v831_get_defect_info)
    v831_init                                0x080057e1   Thumb Code   144  v831_camera.o(i.v831_init)
    v831_is_critical_defect                  0x080058a9   Thumb Code    18  v831_camera.o(i.v831_is_critical_defect)
    v831_rx_callback                         0x080059d9   Thumb Code    54  v831_camera.o(i.v831_rx_callback)
    v831_update                              0x08005b71   Thumb Code    80  v831_camera.o(i.v831_update)
    vl53l0x_check_clamp                      0x08005c05   Thumb Code    20  vl53l0x.o(i.vl53l0x_check_clamp)
    vl53l0x_init                             0x08005ca5   Thumb Code    46  vl53l0x.o(i.vl53l0x_init)
    vl53l0x_read_distance                    0x08005d59   Thumb Code   100  vl53l0x.o(i.vl53l0x_read_distance)
    _get_lc_ctype                            0x08005ec1   Thumb Code    44  lc_ctype_c.o(locale$$code)
    _get_lc_numeric                          0x08005eed   Thumb Code    44  lc_numeric_c.o(locale$$code)
    __fpl_dcheck_NaN1                        0x08005f19   Thumb Code    10  dcheck1.o(x$fpl$dcheck1)
    _fp_init                                 0x08005f29   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x08005f31   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x08005f31   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __ieee_status                            0x08005f33   Thumb Code     6  istatus.o(x$fpl$ieeestatus)
    _printf_fp_dec                           0x08005f39   Thumb Code     4  printf1.o(x$fpl$printf1)
    _printf_fp_hex                           0x08005f3d   Thumb Code     4  printf2.o(x$fpl$printf2)
    __fpl_return_NaN                         0x08005f41   Thumb Code   100  retnan.o(x$fpl$retnan)
    __ARM_scalbn                             0x08005fa5   Thumb Code    92  scalbn.o(x$fpl$scalbn)
    _scanf_real                              0x08006001   Thumb Code     4  scanf1.o(x$fpl$scanf1)
    _scanf_hex_real                          0x08006005   Thumb Code     4  scanf2.o(x$fpl$scanf2)
    _scanf_infnan                            0x08006009   Thumb Code     4  scanf2.o(x$fpl$scanf2)
    __fpl_cmpreturn                          0x0800600d   Thumb Code    48  trapv.o(x$fpl$trapveneer)
    __I$use$fp                               0x0800603c   Number         0  usenofp.o(x$fpl$usenofp)
    font8x16                                 0x0800603c   Data        1536  oled.o(.constdata)
    Region$$Table$$Base                      0x08006904   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08006924   Number         0  anon$$obj.o(Region$$Table)
    __aeabi_HUGE_VAL                         0x08006924   Data           0  fpconst.o(c$$dinf)
    __aeabi_HUGE_VALL                        0x08006924   Data           0  fpconst.o(c$$dinf)
    __aeabi_INFINITY                         0x08006924   Data           0  fpconst.o(c$$dinf)
    __dInf                                   0x08006924   Data           0  fpconst.o(c$$dinf)
    __huge_val                               0x08006924   Data           0  fpconst.o(c$$dinf)
    __dbl_max                                0x0800692c   Data           0  fpconst.o(c$$dmax)
    __ctype                                  0x08006941   Data           0  lc_ctype_c.o(locale$$data)
    __stdout                                 0x2400004c   Data           4  usart.o(.data)
    g_usart_rx_sta                           0x24000050   Data           2  usart.o(.data)
    g_usart_rx_buf                           0x24000a60   Data         200  usart.o(.bss)
    __libspace_start                         0x24000b28   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x24000b88   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080002cd

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00006ab4, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00006a60, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000002cc   Data   RO            3    RESET               startup_stm32h723xx.o
    0x080002cc   0x080002cc   0x00000008   Code   RO         1294  * !!!main             c_w.l(__main.o)
    0x080002d4   0x080002d4   0x00000034   Code   RO         1725    !!!scatter          c_w.l(__scatter.o)
    0x08000308   0x08000308   0x0000001a   Code   RO         1727    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000322   0x08000322   0x00000002   PAD
    0x08000324   0x08000324   0x0000001c   Code   RO         1729    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000340   0x08000340   0x00000000   Code   RO         1267    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x08000340   0x08000340   0x00000006   Code   RO         1394    .ARM.Collect$$_printf_percent$$00000001  c_w.l(_printf_n.o)
    0x08000346   0x08000346   0x00000006   Code   RO         1396    .ARM.Collect$$_printf_percent$$00000002  c_w.l(_printf_p.o)
    0x0800034c   0x0800034c   0x00000006   Code   RO         1266    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x08000352   0x08000352   0x00000006   Code   RO         1399    .ARM.Collect$$_printf_percent$$00000004  c_w.l(_printf_e.o)
    0x08000358   0x08000358   0x00000006   Code   RO         1400    .ARM.Collect$$_printf_percent$$00000005  c_w.l(_printf_g.o)
    0x0800035e   0x0800035e   0x00000006   Code   RO         1401    .ARM.Collect$$_printf_percent$$00000006  c_w.l(_printf_a.o)
    0x08000364   0x08000364   0x0000000a   Code   RO         1406    .ARM.Collect$$_printf_percent$$00000007  c_w.l(_printf_ll.o)
    0x0800036e   0x0800036e   0x00000006   Code   RO         1398    .ARM.Collect$$_printf_percent$$00000008  c_w.l(_printf_i.o)
    0x08000374   0x08000374   0x00000006   Code   RO         1264    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x0800037a   0x0800037a   0x00000006   Code   RO         1265    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x08000380   0x08000380   0x00000006   Code   RO         1397    .ARM.Collect$$_printf_percent$$0000000B  c_w.l(_printf_o.o)
    0x08000386   0x08000386   0x00000006   Code   RO         1395    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x0800038c   0x0800038c   0x00000006   Code   RO         1403    .ARM.Collect$$_printf_percent$$0000000D  c_w.l(_printf_lli.o)
    0x08000392   0x08000392   0x00000006   Code   RO         1404    .ARM.Collect$$_printf_percent$$0000000E  c_w.l(_printf_lld.o)
    0x08000398   0x08000398   0x00000006   Code   RO         1405    .ARM.Collect$$_printf_percent$$0000000F  c_w.l(_printf_llu.o)
    0x0800039e   0x0800039e   0x00000006   Code   RO         1410    .ARM.Collect$$_printf_percent$$00000010  c_w.l(_printf_llo.o)
    0x080003a4   0x080003a4   0x00000006   Code   RO         1411    .ARM.Collect$$_printf_percent$$00000011  c_w.l(_printf_llx.o)
    0x080003aa   0x080003aa   0x0000000a   Code   RO         1407    .ARM.Collect$$_printf_percent$$00000012  c_w.l(_printf_l.o)
    0x080003b4   0x080003b4   0x00000006   Code   RO         1393    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x080003ba   0x080003ba   0x00000006   Code   RO         1263    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x080003c0   0x080003c0   0x00000006   Code   RO         1408    .ARM.Collect$$_printf_percent$$00000015  c_w.l(_printf_lc.o)
    0x080003c6   0x080003c6   0x00000006   Code   RO         1409    .ARM.Collect$$_printf_percent$$00000016  c_w.l(_printf_ls.o)
    0x080003cc   0x080003cc   0x00000004   Code   RO         1402    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x080003d0   0x080003d0   0x00000002   Code   RO         1597    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080003d2   0x080003d2   0x00000004   Code   RO         1420    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x080003d6   0x080003d6   0x00000000   Code   RO         1423    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080003d6   0x080003d6   0x00000000   Code   RO         1426    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080003d6   0x080003d6   0x00000000   Code   RO         1428    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080003d6   0x080003d6   0x00000000   Code   RO         1430    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080003d6   0x080003d6   0x00000006   Code   RO         1431    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x080003dc   0x080003dc   0x00000000   Code   RO         1433    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080003dc   0x080003dc   0x0000000c   Code   RO         1434    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x080003e8   0x080003e8   0x00000000   Code   RO         1435    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080003e8   0x080003e8   0x00000000   Code   RO         1437    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080003e8   0x080003e8   0x0000000a   Code   RO         1438    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x080003f2   0x080003f2   0x00000000   Code   RO         1439    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080003f2   0x080003f2   0x00000000   Code   RO         1441    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080003f2   0x080003f2   0x00000000   Code   RO         1443    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080003f2   0x080003f2   0x00000000   Code   RO         1445    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080003f2   0x080003f2   0x00000000   Code   RO         1447    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080003f2   0x080003f2   0x00000000   Code   RO         1449    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080003f2   0x080003f2   0x00000000   Code   RO         1451    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080003f2   0x080003f2   0x00000000   Code   RO         1453    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080003f2   0x080003f2   0x00000000   Code   RO         1457    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080003f2   0x080003f2   0x00000000   Code   RO         1459    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080003f2   0x080003f2   0x00000000   Code   RO         1461    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080003f2   0x080003f2   0x00000000   Code   RO         1463    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080003f2   0x080003f2   0x00000002   Code   RO         1464    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080003f4   0x080003f4   0x00000002   Code   RO         1683    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080003f6   0x080003f6   0x00000000   Code   RO         1609    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080003f6   0x080003f6   0x00000000   Code   RO         1611    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080003f6   0x080003f6   0x00000000   Code   RO         1613    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x080003f6   0x080003f6   0x00000000   Code   RO         1616    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x080003f6   0x080003f6   0x00000000   Code   RO         1619    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080003f6   0x080003f6   0x00000000   Code   RO         1621    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x080003f6   0x080003f6   0x00000000   Code   RO         1624    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x080003f6   0x080003f6   0x00000002   Code   RO         1625    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x080003f8   0x080003f8   0x00000000   Code   RO         1308    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080003f8   0x080003f8   0x00000000   Code   RO         1476    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080003f8   0x080003f8   0x00000006   Code   RO         1488    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080003fe   0x080003fe   0x00000000   Code   RO         1478    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080003fe   0x080003fe   0x00000004   Code   RO         1479    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000402   0x08000402   0x00000000   Code   RO         1481    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000402   0x08000402   0x00000008   Code   RO         1482    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800040a   0x0800040a   0x00000002   Code   RO         1598    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x0800040c   0x0800040c   0x00000000   Code   RO         1645    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x0800040c   0x0800040c   0x00000004   Code   RO         1646    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000410   0x08000410   0x00000006   Code   RO         1647    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000416   0x08000416   0x00000002   PAD
    0x08000418   0x08000418   0x00000048   Code   RO            4    .text               startup_stm32h723xx.o
    0x08000460   0x08000460   0x00000002   Code   RO         1167    .text               c_w.l(use_no_semi_2.o)
    0x08000462   0x08000462   0x00000002   PAD
    0x08000464   0x08000464   0x00000034   Code   RO         1231    .text               c_w.l(vsnprintf.o)
    0x08000498   0x08000498   0x00000038   Code   RO         1233    .text               c_w.l(__2snprintf.o)
    0x080004d0   0x080004d0   0x00000052   Code   RO         1239    .text               c_w.l(_printf_str.o)
    0x08000522   0x08000522   0x00000002   PAD
    0x08000524   0x08000524   0x00000078   Code   RO         1241    .text               c_w.l(_printf_dec.o)
    0x0800059c   0x0800059c   0x00000188   Code   RO         1260    .text               c_w.l(__printf_flags_ss_wp.o)
    0x08000724   0x08000724   0x0000001a   Code   RO         1268    .text               c_w.l(atoi.o)
    0x0800073e   0x0800073e   0x00000070   Code   RO         1270    .text               c_w.l(strtol.o)
    0x080007ae   0x080007ae   0x00000004   Code   RO         1272    .text               c_w.l(strtok_r.o)
    0x080007b2   0x080007b2   0x00000024   Code   RO         1274    .text               c_w.l(strstr.o)
    0x080007d6   0x080007d6   0x0000003e   Code   RO         1276    .text               c_w.l(strlen.o)
    0x08000814   0x08000814   0x00000096   Code   RO         1278    .text               c_w.l(strncmp.o)
    0x080008aa   0x080008aa   0x0000008a   Code   RO         1280    .text               c_w.l(rt_memcpy_v6.o)
    0x08000934   0x08000934   0x00000064   Code   RO         1282    .text               c_w.l(rt_memcpy_w.o)
    0x08000998   0x08000998   0x00000044   Code   RO         1284    .text               c_w.l(rt_memclr.o)
    0x080009dc   0x080009dc   0x0000004e   Code   RO         1286    .text               c_w.l(rt_memclr_w.o)
    0x08000a2a   0x08000a2a   0x00000056   Code   RO         1288    .text               c_w.l(strncpy.o)
    0x08000a80   0x08000a80   0x00000068   Code   RO         1290    .text               c_w.l(strcmpv7m_pel.o)
    0x08000ae8   0x08000ae8   0x00000006   Code   RO         1292    .text               c_w.l(heapauxi.o)
    0x08000aee   0x08000aee   0x00000002   Code   RO         1304    .text               c_w.l(use_no_semi.o)
    0x08000af0   0x08000af0   0x00000010   Code   RO         1309    .text               c_w.l(rt_ctype_table.o)
    0x08000b00   0x08000b00   0x00000008   Code   RO         1319    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x08000b08   0x08000b08   0x00000016   Code   RO         1341    .text               c_w.l(_rserrno.o)
    0x08000b1e   0x08000b1e   0x0000004e   Code   RO         1343    .text               c_w.l(_printf_pad.o)
    0x08000b6c   0x08000b6c   0x00000024   Code   RO         1345    .text               c_w.l(_printf_truncate.o)
    0x08000b90   0x08000b90   0x000000b2   Code   RO         1347    .text               c_w.l(_printf_intcommon.o)
    0x08000c42   0x08000c42   0x00000028   Code   RO         1349    .text               c_w.l(_printf_charcount.o)
    0x08000c6a   0x08000c6a   0x0000041e   Code   RO         1351    .text               c_w.l(_printf_fp_dec.o)
    0x08001088   0x08001088   0x00000030   Code   RO         1353    .text               c_w.l(_printf_char_common.o)
    0x080010b8   0x080010b8   0x0000000a   Code   RO         1355    .text               c_w.l(_sputc.o)
    0x080010c2   0x080010c2   0x00000010   Code   RO         1357    .text               c_w.l(_snputc.o)
    0x080010d2   0x080010d2   0x0000002c   Code   RO         1359    .text               c_w.l(_printf_char.o)
    0x080010fe   0x080010fe   0x00000002   PAD
    0x08001100   0x08001100   0x000000bc   Code   RO         1361    .text               c_w.l(_printf_wctomb.o)
    0x080011bc   0x080011bc   0x0000007c   Code   RO         1364    .text               c_w.l(_printf_longlong_dec.o)
    0x08001238   0x08001238   0x00000070   Code   RO         1370    .text               c_w.l(_printf_oct_int_ll.o)
    0x080012a8   0x080012a8   0x00000094   Code   RO         1390    .text               c_w.l(_printf_hex_int_ll_ptr.o)
    0x0800133c   0x0800133c   0x0000009e   Code   RO         1412    .text               c_w.l(_strtoul.o)
    0x080013da   0x080013da   0x00000002   PAD
    0x080013dc   0x080013dc   0x000000a4   Code   RO         1414    .text               c_w.l(strtod.o)
    0x08001480   0x08001480   0x00000044   Code   RO         1416    .text               c_w.l(strtok_int.o)
    0x080014c4   0x080014c4   0x00000008   Code   RO         1472    .text               c_w.l(libspace.o)
    0x080014cc   0x080014cc   0x00000008   Code   RO         1493    .text               c_w.l(rt_locale_intlibspace.o)
    0x080014d4   0x080014d4   0x0000008a   Code   RO         1503    .text               c_w.l(lludiv10.o)
    0x0800155e   0x0800155e   0x00000012   Code   RO         1505    .text               c_w.l(isspace.o)
    0x08001570   0x08001570   0x000002fc   Code   RO         1507    .text               c_w.l(_printf_fp_hex.o)
    0x0800186c   0x0800186c   0x00000080   Code   RO         1510    .text               c_w.l(_printf_fp_infnan.o)
    0x080018ec   0x080018ec   0x0000002c   Code   RO         1514    .text               c_w.l(_printf_wchar.o)
    0x08001918   0x08001918   0x0000001c   Code   RO         1516    .text               c_w.l(_chval.o)
    0x08001934   0x08001934   0x00000040   Code   RO         1518    .text               c_w.l(_sgetc.o)
    0x08001974   0x08001974   0x000000e4   Code   RO         1520    .text               c_w.l(bigflt0.o)
    0x08001a58   0x08001a58   0x00000040   Code   RO         1545    .text               c_w.l(_wcrtomb.o)
    0x08001a98   0x08001a98   0x00000020   Code   RO         1557    .text               c_w.l(strcspn.o)
    0x08001ab8   0x08001ab8   0x0000001c   Code   RO         1559    .text               c_w.l(strspn.o)
    0x08001ad4   0x08001ad4   0x0000004a   Code   RO         1574    .text               c_w.l(sys_stackheap_outer.o)
    0x08001b1e   0x08001b1e   0x00000002   PAD
    0x08001b20   0x08001b20   0x000004f8   Code   RO         1580    .text               c_w.l(scanf_fp.o)
    0x08002018   0x08002018   0x00000012   Code   RO         1582    .text               c_w.l(exit.o)
    0x0800202a   0x0800202a   0x00000002   PAD
    0x0800202c   0x0800202c   0x00000320   Code   RO         1651    .text               c_w.l(scanf_hexfp.o)
    0x0800234c   0x0800234c   0x00000134   Code   RO         1653    .text               c_w.l(scanf_infnan.o)
    0x08002480   0x08002480   0x00000026   Code   RO         1663    .text               c_w.l(llshl.o)
    0x080024a6   0x080024a6   0x0000003e   Code   RO         1523    CL$$btod_d2e        c_w.l(btod.o)
    0x080024e4   0x080024e4   0x00000046   Code   RO         1525    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x0800252a   0x0800252a   0x00000060   Code   RO         1524    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x0800258a   0x0800258a   0x00000338   Code   RO         1533    CL$$btod_div_common  c_w.l(btod.o)
    0x080028c2   0x080028c2   0x00000002   PAD
    0x080028c4   0x080028c4   0x00000084   Code   RO         1531    CL$$btod_e2d        c_w.l(btod.o)
    0x08002948   0x08002948   0x000000dc   Code   RO         1530    CL$$btod_e2e        c_w.l(btod.o)
    0x08002a24   0x08002a24   0x0000002a   Code   RO         1527    CL$$btod_ediv       c_w.l(btod.o)
    0x08002a4e   0x08002a4e   0x0000002a   Code   RO         1529    CL$$btod_edivd      c_w.l(btod.o)
    0x08002a78   0x08002a78   0x0000002a   Code   RO         1526    CL$$btod_emul       c_w.l(btod.o)
    0x08002aa2   0x08002aa2   0x0000002a   Code   RO         1528    CL$$btod_emuld      c_w.l(btod.o)
    0x08002acc   0x08002acc   0x00000244   Code   RO         1532    CL$$btod_mult_common  c_w.l(btod.o)
    0x08002d10   0x08002d10   0x0000000c   Code   RO           13    i.HAL_GetTick       main.o
    0x08002d1c   0x08002d1c   0x00000010   Code   RO           14    i.SysTick_Handler   main.o
    0x08002d2c   0x08002d2c   0x000000a4   Code   RO         1050    i.USART1_IRQHandler  usart.o
    0x08002dd0   0x08002dd0   0x0000004c   Code   RO          667    i.USART2_IRQHandler  gps.o
    0x08002e1c   0x08002e1c   0x0000003c   Code   RO          747    i.USART3_IRQHandler  esp8266.o
    0x08002e58   0x08002e58   0x00000030   Code   RO         1572    i.__ARM_fpclassify  m_wv.l(fpclassify.o)
    0x08002e88   0x08002e88   0x000000dc   Code   RO         1636    i.__hardfp___mathlib_tofloat  m_wv.l(narrow.o)
    0x08002f64   0x08002f64   0x00000004   PAD
    0x08002f68   0x08002f68   0x00000038   Code   RO         1298    i.__hardfp_atof     m_wv.l(atof.o)
    0x08002fa0   0x08002fa0   0x00000018   Code   RO         1706    i.__mathlib_dbl_overflow  m_wv.l(dunder.o)
    0x08002fb8   0x08002fb8   0x00000018   Code   RO         1708    i.__mathlib_dbl_underflow  m_wv.l(dunder.o)
    0x08002fd0   0x08002fd0   0x00000012   Code   RO         1637    i.__mathlib_narrow  m_wv.l(narrow.o)
    0x08002fe2   0x08002fe2   0x00000006   PAD
    0x08002fe8   0x08002fe8   0x000000b8   Code   RO         1686    i.__support_ldexp   m_wv.l(ldexp.o)
    0x080030a0   0x080030a0   0x0000000e   Code   RO         1253    i._is_digit         c_w.l(__printf_wp.o)
    0x080030ae   0x080030ae   0x00000004   Code   RO         1052    i._sys_exit         usart.o
    0x080030b2   0x080030b2   0x00000002   PAD
    0x080030b4   0x080030b4   0x0000002c   Code   RO          869    i.delay_init        delay.o
    0x080030e0   0x080030e0   0x00000012   Code   RO          870    i.delay_ms          delay.o
    0x080030f2   0x080030f2   0x00000002   PAD
    0x080030f4   0x080030f4   0x00000048   Code   RO          871    i.delay_us          delay.o
    0x0800313c   0x0800313c   0x00000014   Code   RO          748    i.esp8266_clear_error  esp8266.o
    0x08003150   0x08003150   0x000000b4   Code   RO          749    i.esp8266_connect_ap  esp8266.o
    0x08003204   0x08003204   0x00000354   Code   RO          750    i.esp8266_connect_mqtt  esp8266.o
    0x08003558   0x08003558   0x0000000c   Code   RO          751    i.esp8266_get_error  esp8266.o
    0x08003564   0x08003564   0x00000064   Code   RO          752    i.esp8266_init      esp8266.o
    0x080035c8   0x080035c8   0x0000003c   Code   RO          753    i.esp8266_is_mqtt_connected  esp8266.o
    0x08003604   0x08003604   0x00000098   Code   RO          754    i.esp8266_mqtt_ping  esp8266.o
    0x0800369c   0x0800369c   0x0000022c   Code   RO          756    i.esp8266_mqtt_publish_defect  esp8266.o
    0x080038c8   0x080038c8   0x000000f4   Code   RO          760    i.esp8266_send_cmd  esp8266.o
    0x080039bc   0x080039bc   0x000000f8   Code   RO          763    i.esp8266_uart_init  esp8266.o
    0x08003ab4   0x08003ab4   0x00000004   PAD
    0x08003ab8   0x08003ab8   0x0000008c   Code   RO         1659    i.frexp             m_wv.l(frexp.o)
    0x08003b44   0x08003b44   0x00000020   Code   RO          439    i.get_font_data     oled.o
    0x08003b64   0x08003b64   0x00000008   Code   RO          239    i.get_system_ms     vl53l0x.o
    0x08003b6c   0x08003b6c   0x0000002c   Code   RO          668    i.gps_calc_checksum  gps.o
    0x08003b98   0x08003b98   0x00000010   Code   RO          669    i.gps_get_location  gps.o
    0x08003ba8   0x08003ba8   0x00000038   Code   RO          670    i.gps_init          gps.o
    0x08003be0   0x08003be0   0x00000060   Code   RO          671    i.gps_nmea_to_decimal  gps.o
    0x08003c40   0x08003c40   0x000000a0   Code   RO          672    i.gps_parse_data    gps.o
    0x08003ce0   0x08003ce0   0x000000dc   Code   RO          673    i.gps_parse_gga     gps.o
    0x08003dbc   0x08003dbc   0x000000d8   Code   RO          674    i.gps_parse_rmc     gps.o
    0x08003e94   0x08003e94   0x00000118   Code   RO          675    i.gps_uart_init     gps.o
    0x08003fac   0x08003fac   0x00000028   Code   RO          676    i.gps_update        gps.o
    0x08003fd4   0x08003fd4   0x00000074   Code   RO          677    i.gps_verify_checksum  gps.o
    0x08004048   0x08004048   0x00000038   Code   RO          240    i.i2c_ack           vl53l0x.o
    0x08004080   0x08004080   0x0000000a   Code   RO          241    i.i2c_delay         vl53l0x.o
    0x0800408a   0x0800408a   0x00000002   PAD
    0x0800408c   0x0800408c   0x00000070   Code   RO          440    i.i2c_init          oled.o
    0x080040fc   0x080040fc   0x00000038   Code   RO          242    i.i2c_nack          vl53l0x.o
    0x08004134   0x08004134   0x00000088   Code   RO          243    i.i2c_read_byte     vl53l0x.o
    0x080041bc   0x080041bc   0x0000005c   Code   RO          244    i.i2c_send_byte     vl53l0x.o
    0x08004218   0x08004218   0x0000003c   Code   RO          245    i.i2c_start         vl53l0x.o
    0x08004254   0x08004254   0x0000003c   Code   RO          246    i.i2c_stop          vl53l0x.o
    0x08004290   0x08004290   0x00000070   Code   RO          247    i.i2c_wait_ack      vl53l0x.o
    0x08004300   0x08004300   0x00000002   Code   RO           15    i.iwdg_feed         main.o
    0x08004302   0x08004302   0x00000002   Code   RO           16    i.iwdg_init         main.o
    0x08004304   0x08004304   0x0000005c   Code   RO           17    i.main              main.o
    0x08004360   0x08004360   0x00000038   Code   RO          542    i.motor_init        motor.o
    0x08004398   0x08004398   0x0000001c   Code   RO          543    i.motor_start       motor.o
    0x080043b4   0x080043b4   0x00000020   Code   RO          544    i.motor_stop        motor.o
    0x080043d4   0x080043d4   0x0000003a   Code   RO          441    i.oled_clear        oled.o
    0x0800440e   0x0800440e   0x0000006e   Code   RO          442    i.oled_display_char  oled.o
    0x0800447c   0x0800447c   0x00000084   Code   RO          444    i.oled_draw_point   oled.o
    0x08004500   0x08004500   0x0000004c   Code   RO          445    i.oled_init         oled.o
    0x0800454c   0x0800454c   0x000000a0   Code   RO          446    i.oled_printf       oled.o
    0x080045ec   0x080045ec   0x00000024   Code   RO          448    i.oled_set_cursor   oled.o
    0x08004610   0x08004610   0x00000068   Code   RO          449    i.oled_write_cmd    oled.o
    0x08004678   0x08004678   0x00000080   Code   RO          450    i.oled_write_data   oled.o
    0x080046f8   0x080046f8   0x00000014   Code   RO          383    i.servo_clamp_close  servo.o
    0x0800470c   0x0800470c   0x00000014   Code   RO          384    i.servo_clamp_open  servo.o
    0x08004720   0x08004720   0x0000003c   Code   RO          385    i.servo_generate_pwm  servo.o
    0x0800475c   0x0800475c   0x0000003c   Code   RO          387    i.servo_init        servo.o
    0x08004798   0x08004798   0x00000054   Code   RO          388    i.servo_set_position  servo.o
    0x080047ec   0x080047ec   0x0000001c   Code   RO           18    i.state_change      main.o
    0x08004808   0x08004808   0x000006fc   Code   RO           19    i.state_machine_process  main.o
    0x08004f04   0x08004f04   0x00000140   Code   RO          924    i.sys_cache_enable  sys.o
    0x08005044   0x08005044   0x00000384   Code   RO          925    i.sys_clock_set     sys.o
    0x080053c8   0x080053c8   0x00000068   Code   RO          926    i.sys_gpio_af_set   sys.o
    0x08005430   0x08005430   0x00000010   Code   RO          927    i.sys_gpio_pin_get  sys.o
    0x08005440   0x08005440   0x00000018   Code   RO          928    i.sys_gpio_pin_set  sys.o
    0x08005458   0x08005458   0x000000de   Code   RO          929    i.sys_gpio_set      sys.o
    0x08005536   0x08005536   0x00000002   PAD
    0x08005538   0x08005538   0x00000078   Code   RO          934    i.sys_nvic_init     sys.o
    0x080055b0   0x080055b0   0x00000028   Code   RO          935    i.sys_nvic_priority_group_config  sys.o
    0x080055d8   0x080055d8   0x00000010   Code   RO          936    i.sys_nvic_set_vector_table  sys.o
    0x080055e8   0x080055e8   0x00000060   Code   RO          939    i.sys_stm32_clock_init  sys.o
    0x08005648   0x08005648   0x0000003c   Code   RO           21    i.system_init       main.o
    0x08005684   0x08005684   0x0000001e   Code   RO           22    i.systick_init      main.o
    0x080056a2   0x080056a2   0x00000002   PAD
    0x080056a4   0x080056a4   0x0000003c   Code   RO          248    i.update_distance_status  vl53l0x.o
    0x080056e0   0x080056e0   0x000000e4   Code   RO         1055    i.usart_init        usart.o
    0x080057c4   0x080057c4   0x0000000c   Code   RO         1056    i.usart_register_rx_callback  usart.o
    0x080057d0   0x080057d0   0x00000010   Code   RO          161    i.v831_get_defect_info  v831_camera.o
    0x080057e0   0x080057e0   0x000000c8   Code   RO          162    i.v831_init         v831_camera.o
    0x080058a8   0x080058a8   0x00000012   Code   RO          163    i.v831_is_critical_defect  v831_camera.o
    0x080058ba   0x080058ba   0x00000002   PAD
    0x080058bc   0x080058bc   0x0000011c   Code   RO          164    i.v831_parse_data   v831_camera.o
    0x080059d8   0x080059d8   0x00000044   Code   RO          165    i.v831_rx_callback  v831_camera.o
    0x08005a1c   0x08005a1c   0x00000054   Code   RO          166    i.v831_send_command  v831_camera.o
    0x08005a70   0x08005a70   0x00000100   Code   RO          167    i.v831_uart_init    v831_camera.o
    0x08005b70   0x08005b70   0x00000060   Code   RO          168    i.v831_update       v831_camera.o
    0x08005bd0   0x08005bd0   0x00000034   Code   RO          169    i.v831_wait_response  v831_camera.o
    0x08005c04   0x08005c04   0x00000018   Code   RO          249    i.vl53l0x_check_clamp  vl53l0x.o
    0x08005c1c   0x08005c1c   0x00000024   Code   RO          250    i.vl53l0x_check_id  vl53l0x.o
    0x08005c40   0x08005c40   0x00000064   Code   RO          252    i.vl53l0x_i2c_init  vl53l0x.o
    0x08005ca4   0x08005ca4   0x00000038   Code   RO          253    i.vl53l0x_init      vl53l0x.o
    0x08005cdc   0x08005cdc   0x00000020   Code   RO          254    i.vl53l0x_poll_ranging  vl53l0x.o
    0x08005cfc   0x08005cfc   0x0000005a   Code   RO          255    i.vl53l0x_read_byte  vl53l0x.o
    0x08005d56   0x08005d56   0x00000002   PAD
    0x08005d58   0x08005d58   0x00000070   Code   RO          256    i.vl53l0x_read_distance  vl53l0x.o
    0x08005dc8   0x08005dc8   0x00000076   Code   RO          257    i.vl53l0x_read_multi  vl53l0x.o
    0x08005e3e   0x08005e3e   0x00000026   Code   RO          258    i.vl53l0x_read_range_mm  vl53l0x.o
    0x08005e64   0x08005e64   0x0000000c   Code   RO          259    i.vl53l0x_start_ranging  vl53l0x.o
    0x08005e70   0x08005e70   0x0000004e   Code   RO          260    i.vl53l0x_write_byte  vl53l0x.o
    0x08005ebe   0x08005ebe   0x00000002   PAD
    0x08005ec0   0x08005ec0   0x0000002c   Code   RO         1548    locale$$code        c_w.l(lc_ctype_c.o)
    0x08005eec   0x08005eec   0x0000002c   Code   RO         1551    locale$$code        c_w.l(lc_numeric_c.o)
    0x08005f18   0x08005f18   0x00000010   Code   RO         1717    x$fpl$dcheck1       fz_wv.l(dcheck1.o)
    0x08005f28   0x08005f28   0x0000000a   Code   RO         1566    x$fpl$fpinit        fz_wv.l(fpinit.o)
    0x08005f32   0x08005f32   0x00000006   Code   RO         1568    x$fpl$ieeestatus    fz_wv.l(istatus.o)
    0x08005f38   0x08005f38   0x00000004   Code   RO         1296    x$fpl$printf1       fz_wv.l(printf1.o)
    0x08005f3c   0x08005f3c   0x00000004   Code   RO         1465    x$fpl$printf2       fz_wv.l(printf2.o)
    0x08005f40   0x08005f40   0x00000064   Code   RO         1719    x$fpl$retnan        fz_wv.l(retnan.o)
    0x08005fa4   0x08005fa4   0x0000005c   Code   RO         1700    x$fpl$scalbn        fz_wv.l(scalbn.o)
    0x08006000   0x08006000   0x00000004   Code   RO         1570    x$fpl$scanf1        fz_wv.l(scanf1.o)
    0x08006004   0x08006004   0x00000008   Code   RO         1630    x$fpl$scanf2        fz_wv.l(scanf2.o)
    0x0800600c   0x0800600c   0x00000030   Code   RO         1721    x$fpl$trapveneer    fz_wv.l(trapv.o)
    0x0800603c   0x0800603c   0x00000000   Code   RO         1471    x$fpl$usenofp       fz_wv.l(usenofp.o)
    0x0800603c   0x0800603c   0x00000600   Data   RO          452    .constdata          oled.o
    0x0800663c   0x0800663c   0x00000010   Data   RO          765    .constdata          esp8266.o
    0x0800664c   0x0800664c   0x00000011   Data   RO         1261    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x0800665d   0x0800665d   0x00000003   PAD
    0x08006660   0x08006660   0x00000008   Data   RO         1362    .constdata          c_w.l(_printf_wctomb.o)
    0x08006668   0x08006668   0x00000028   Data   RO         1391    .constdata          c_w.l(_printf_hex_int_ll_ptr.o)
    0x08006690   0x08006690   0x00000026   Data   RO         1508    .constdata          c_w.l(_printf_fp_hex.o)
    0x080066b6   0x080066b6   0x00000002   PAD
    0x080066b8   0x080066b8   0x00000094   Data   RO         1521    .constdata          c_w.l(bigflt0.o)
    0x0800674c   0x0800674c   0x000001b8   Data   RO          766    .conststring        esp8266.o
    0x08006904   0x08006904   0x00000020   Data   RO         1723    Region$$Table       anon$$obj.o
    0x08006924   0x08006924   0x00000008   Data   RO         1626    c$$dinf             fz_wv.l(fpconst.o)
    0x0800692c   0x0800692c   0x00000008   Data   RO         1629    c$$dmax             fz_wv.l(fpconst.o)
    0x08006934   0x08006934   0x00000110   Data   RO         1547    locale$$data        c_w.l(lc_ctype_c.o)
    0x08006a44   0x08006a44   0x0000001c   Data   RO         1550    locale$$data        c_w.l(lc_numeric_c.o)


    Execution Region RW_IRAM2 (Exec base: 0x24000000, Load base: 0x08006a60, Size: 0x00001388, Max: 0x00050000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x24000000   0x08006a60   0x0000001c   Data   RW           23    .data               main.o
    0x2400001c   0x08006a7c   0x0000000b   Data   RW          171    .data               v831_camera.o
    0x24000027   0x08006a87   0x00000001   PAD
    0x24000028   0x08006a88   0x00000008   Data   RW          261    .data               vl53l0x.o
    0x24000030   0x08006a90   0x00000001   Data   RW          389    .data               servo.o
    0x24000031   0x08006a91   0x00000002   Data   RW          453    .data               oled.o
    0x24000033   0x08006a93   0x00000001   Data   RW          545    .data               motor.o
    0x24000034   0x08006a94   0x00000002   Data   RW          679    .data               gps.o
    0x24000036   0x08006a96   0x00000002   PAD
    0x24000038   0x08006a98   0x0000000c   Data   RW          767    .data               esp8266.o
    0x24000044   0x08006aa4   0x00000004   Data   RW          872    .data               delay.o
    0x24000048   0x08006aa8   0x0000000a   Data   RW         1058    .data               usart.o
    0x24000052   0x08006ab2   0x00000002   PAD
    0x24000054        -       0x00000200   Zero   RW          170    .bss                v831_camera.o
    0x24000254        -       0x00000400   Zero   RW          451    .bss                oled.o
    0x24000654        -       0x0000020c   Zero   RW          678    .bss                gps.o
    0x24000860        -       0x00000200   Zero   RW          764    .bss                esp8266.o
    0x24000a60        -       0x000000c8   Zero   RW         1057    .bss                usart.o
    0x24000b28        -       0x00000060   Zero   RW         1473    .bss                c_w.l(libspace.o)
    0x24000b88        -       0x00000000   Zero   RW            2    HEAP                startup_stm32h723xx.o
    0x24000b88        -       0x00000800   Zero   RW            1    STACK               startup_stm32h723xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       134         10          0          4          0     889747   delay.o
      2484        610        456         12        512       9407   esp8266.o
      1320        130          0          2        524       7991   gps.o
      2030        650          0         28          0     901071   main.o
       116         30          0          1          0       1644   motor.o
       948         78       1536          2       1024       8563   oled.o
       244         38          0          1          0       2946   servo.o
        72         22        716          0       2048        984   startup_stm32h723xx.o
      1858         50          0          0          0      43528   sys.o
       408         40          0         10        200       4382   usart.o
      1074        174          0         11        512      42253   v831_camera.o
      1346         80          0          8          0      12641   vl53l0x.o

    ----------------------------------------------------------------------
     12054       <USER>       <GROUP>         84       4820    1925157   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        20          4          0          5          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        56          6          0          0          0         88   __2snprintf.o
         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        28          0          0          0          0         68   _chval.o
         6          0          0          0          0          0   _printf_a.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        40          0          0          0          0         68   _printf_charcount.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_e.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       764          8         38          0          0        100   _printf_fp_hex.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         6          0          0          0          0          0   _printf_g.o
       148          4         40          0          0        160   _printf_hex_int_ll_ptr.o
         6          0          0          0          0          0   _printf_i.o
       178          0          0          0          0         88   _printf_intcommon.o
        10          0          0          0          0          0   _printf_l.o
         6          0          0          0          0          0   _printf_lc.o
        10          0          0          0          0          0   _printf_ll.o
         6          0          0          0          0          0   _printf_lld.o
         6          0          0          0          0          0   _printf_lli.o
         6          0          0          0          0          0   _printf_llo.o
         6          0          0          0          0          0   _printf_llu.o
         6          0          0          0          0          0   _printf_llx.o
       124         16          0          0          0         92   _printf_longlong_dec.o
         6          0          0          0          0          0   _printf_ls.o
         6          0          0          0          0          0   _printf_n.o
         6          0          0          0          0          0   _printf_o.o
       112         10          0          0          0        124   _printf_oct_int_ll.o
         6          0          0          0          0          0   _printf_p.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        36          0          0          0          0         84   _printf_truncate.o
         6          0          0          0          0          0   _printf_u.o
        44          0          0          0          0        108   _printf_wchar.o
       188          6          8          0          0         92   _printf_wctomb.o
         6          0          0          0          0          0   _printf_x.o
        22          0          0          0          0        100   _rserrno.o
        64          0          0          0          0         84   _sgetc.o
        16          0          0          0          0         68   _snputc.o
        10          0          0          0          0         68   _sputc.o
       158          0          0          0          0         92   _strtoul.o
        64          0          0          0          0         92   _wcrtomb.o
        26          0          0          0          0         80   atoi.o
       228          4        148          0          0         96   bigflt0.o
      2152        136          0          0          0        960   btod.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
        18          0          0          0          0         76   isspace.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        34          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        38          0          0          0          0         68   llshl.o
       138          0          0          0          0         80   lludiv10.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
      1272         16          0          0          0        168   scanf_fp.o
       800         14          0          0          0        100   scanf_hexfp.o
       308         16          0          0          0        100   scanf_infnan.o
       104          0          0          0          0         68   strcmpv7m_pel.o
        32          0          0          0          0         80   strcspn.o
        62          0          0          0          0         76   strlen.o
       150          0          0          0          0         80   strncmp.o
        86          0          0          0          0         76   strncpy.o
        28          0          0          0          0         80   strspn.o
        36          0          0          0          0         80   strstr.o
       164         14          0          0          0        120   strtod.o
        68          4          0          0          0         84   strtok_int.o
         4          0          0          0          0         68   strtok_r.o
       112          0          0          0          0         88   strtol.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
        52          4          0          0          0         80   vsnprintf.o
        16          4          0          0          0        116   dcheck1.o
         0          0         16          0          0          0   fpconst.o
        10          0          0          0          0        116   fpinit.o
         6          0          0          0          0        116   istatus.o
         4          0          0          0          0        116   printf1.o
         4          0          0          0          0        116   printf2.o
       100          0          0          0          0        116   retnan.o
        92          0          0          0          0        116   scalbn.o
         4          0          0          0          0        116   scanf1.o
         8          0          0          0          0        132   scanf2.o
        48          0          0          0          0        116   trapv.o
         0          0          0          0          0          0   usenofp.o
        56         12          0          0          0        132   atof.o
        48         28          0          0          0        232   dunder.o
        48          0          0          0          0        124   fpclassify.o
       140         22          0          0          0        128   frexp.o
       184         14          0          0          0        156   ldexp.o
       238         26          0          0          0        304   narrow.o

    ----------------------------------------------------------------------
     11866        <USER>        <GROUP>          0         96       8724   Library Totals
        28          6          5          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

     10832        344        551          0         96       6472   c_w.l
       292          4         16          0          0       1176   fz_wv.l
       714        102          0          0          0       1076   m_wv.l

    ----------------------------------------------------------------------
     11866        <USER>        <GROUP>          0         96       8724   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     23920       2372       3312         84       4916    1919441   Grand Totals
     23920       2372       3312         84       4916    1919441   ELF Image Totals
     23920       2372       3312         84          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                27232 (  26.59kB)
    Total RW  Size (RW Data + ZI Data)              5000 (   4.88kB)
    Total ROM Size (Code + RO Data + RW Data)      27316 (  26.68kB)

==============================================================================

