/**
 * @file connection_test.c
 * @brief 简化的连接测试程序
 * <AUTHOR> Assistant
 * @date 2025-07-25
 */

#include "sys.h"
#include "delay.h"
#include "esp8266.h"
#include "gps.h"
#include "v831_camera.h"
#include "oled.h"

/**
 * @brief 简单的MQTT连接测试
 * @retval 测试结果字符串
 */
const char* simple_mqtt_test(void)
{
    /* 1. 测试WiFi连接 */
    if (!esp8266_connect_ap())
    {
        return "WiFi Failed";
    }
    
    /* 2. 测试MQTT连接 */
    if (!esp8266_connect_mqtt())
    {
        return "MQTT Failed";
    }
    
    /* 3. 测试数据发送 */
    uint8_t send_result = esp8266_mqtt_publish_defect(
        31.230416,  /* 测试纬度 */
        121.473701, /* 测试经度 */
        1,          /* 测试缺陷类型 - YASHANG */
        85,         /* 测试置信度 */
        1           /* 测试缺陷计数 */
    );
    
    if (!send_result)
    {
        return "Send Failed";
    }
    
    return "All OK";
}

/**
 * @brief 显示连接状态
 */
void display_connection_status(void)
{
    const char* result = simple_mqtt_test();
    
    oled_clear();
    oled_set_cursor(0, 0);
    oled_printf("MQTT Test Result:");
    oled_set_cursor(0, 1);
    oled_printf("%s", result);
    
    /* 显示MQTT参数信息 */
    oled_set_cursor(0, 2);
    oled_printf("Host: a1lod4v9cR");
    oled_set_cursor(0, 3);
    oled_printf("Port: 1883");
}

/**
 * @brief 测试GPS功能
 */
void test_gps_function(void)
{
    gps_location_t location;
    uint8_t test_count = 0;
    
    oled_clear();
    oled_set_cursor(0, 0);
    oled_printf("GPS Test...");
    
    while (test_count < 10)  /* 测试10秒 */
    {
        gps_update();
        location = gps_get_location();
        
        oled_set_cursor(0, 1);
        oled_printf("Time: %ds", test_count);
        oled_set_cursor(0, 2);
        oled_printf("Fixed: %s", location.fixed ? "YES" : "NO");
        
        if (location.fixed)
        {
            oled_set_cursor(0, 3);
            oled_printf("Lat:%.4f", location.latitude);
            break;
        }
        
        delay_ms(1000);
        test_count++;
    }
}

/**
 * @brief 测试缺陷检测功能
 */
void test_defect_detection(void)
{
    defect_info_t defect;
    
    oled_clear();
    oled_set_cursor(0, 0);
    oled_printf("Defect Test...");
    
    for (uint8_t i = 0; i < 5; i++)
    {
        v831_update();
        defect = v831_get_defect_info();
        
        oled_set_cursor(0, 1);
        oled_printf("Detected: %s", defect.detected ? "YES" : "NO");
        oled_set_cursor(0, 2);
        oled_printf("Type: %d", defect.type);
        oled_set_cursor(0, 3);
        oled_printf("Conf: %d%%", defect.confidence);
        
        delay_ms(1000);
    }
}

/**
 * @brief 运行所有测试
 */
void run_all_tests(void)
{
    /* 显示开始信息 */
    oled_clear();
    oled_set_cursor(0, 0);
    oled_printf("Starting Tests...");
    delay_ms(2000);
    
    /* 1. MQTT连接测试 */
    display_connection_status();
    delay_ms(5000);
    
    /* 2. GPS测试 */
    test_gps_function();
    delay_ms(3000);
    
    /* 3. 缺陷检测测试 */
    test_defect_detection();
    delay_ms(3000);
    
    /* 显示完成信息 */
    oled_clear();
    oled_set_cursor(0, 0);
    oled_printf("Tests Completed");
    oled_set_cursor(0, 1);
    oled_printf("Check results");
    oled_set_cursor(0, 2);
    oled_printf("above");
}
