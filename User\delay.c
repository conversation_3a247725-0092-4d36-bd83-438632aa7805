#include "delay.h"
#include "sys.h"

/**
 * @brief       初始化延时函数
 * @param       SYSCLK: 系统时钟频率，单位MHz
 * @retval      无
 * @note        本函数仅保持兼容性，实际延时由sys模块处理
 */
void delay_init(uint16_t SYSCLK)
{
    /* 空函数，实际初始化在sys_stm32_clock_init中完成 */
    (void)SYSCLK;  /* 避免编译器警告 */
}

/**
 * @brief       延时毫秒函数
 * @param       nms: 要延时的毫秒数
 * @retval      无
 */
void delay_ms(uint16_t nms)
{
    sys_delay_ms((uint32_t)nms);
}

/**
 * @brief       延时微秒函数
 * @param       nus: 要延时的微秒数
 * @retval      无
 */
void delay_us(uint32_t nus)
{
    sys_delay_us(nus);
} 