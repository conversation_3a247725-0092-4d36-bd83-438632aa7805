#include "sys.h"
#include "delay.h"
#include "../Drivers/SYSTEM/usart/usart.h"
#include "gps.h"
#include "pinconfig.h"
#include <string.h>
#include <stdlib.h>

#define GPS_RX_BUFFER_SIZE 512
static uint8_t gps_rx_buffer[GPS_RX_BUFFER_SIZE];
static uint16_t gps_rx_count = 0;
static gps_location_t current_location = {0, 0, 0};

/**
 * @brief       GPS UART初始化
 * @param       无
 * @retval      无
 */
static void gps_uart_init(void)
{
    /* 使能GPIO时钟 */
    GPS_TX_GPIO_CLK;  /* 使用pinconfig.h中定义的宏 */
    GPS_RX_GPIO_CLK;
    
    /* 使能USART2时钟 */
    GPS_USART_CLK_ENABLE();  /* 使用pinconfig.h中定义的宏 */
    
    /* 配置GPIO引脚 */
    sys_gpio_set(GPS_TX_GPIO_PORT, 1 << GPS_TX_PIN, 
                 SYS_GPIO_MODE_AF, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);
    
    sys_gpio_set(GPS_RX_GPIO_PORT, 1 << GPS_RX_PIN, 
                 SYS_GPIO_MODE_AF, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);
    
    /* 配置GPIO的AF功能 */
    sys_gpio_af_set(GPS_TX_GPIO_PORT, GPS_TX_PIN, 7);  /* 设置为USART2的AF7 */
    sys_gpio_af_set(GPS_RX_GPIO_PORT, GPS_RX_PIN, 7);  /* 设置为USART2的AF7 */
    
    /* 配置PD11为输出，用于GPS模块控制 */
    GPS_PD_GPIO_CLK;
    sys_gpio_set(GPS_PD_GPIO_PORT, 1 << GPS_PD_PIN,
                 SYS_GPIO_MODE_OUT, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_MID, SYS_GPIO_PUPD_PU);

    GPS_USART->CR1 = 0;
    GPS_USART->CR1 |= 1 << 0;
    GPS_USART->CR1 |= 1 << 2;
    GPS_USART->CR1 |= 1 << 5;

    uint32_t temp = (130 * 1000000 + 9600 / 2) / 9600;
    GPS_USART->BRR = temp;

    GPS_USART->CR1 |= 1 << 0;

    NVIC_SetPriority(USART2_IRQn, 3);
    NVIC_EnableIRQ(USART2_IRQn);
}

/**
 * @brief       GPS模块初始化
 * @param       无
 * @retval      无
 */
void gps_init(void)
{
    /* 初始化UART接口 */
    gps_uart_init();
    
    /* 清空GPS接收缓冲区 */
    memset(gps_rx_buffer, 0, GPS_RX_BUFFER_SIZE);
    gps_rx_count = 0;
    
    /* 使能GPS模块 */
    sys_gpio_pin_set(GPS_PD_GPIO_PORT, 1 << GPS_PD_PIN, 1);
    
    /* 等待GPS模块启动 */
    delay_ms(1000);
}

/**
 * @brief       USART2中断服务函数
 * @param       无
 * @retval      无
 */
void USART2_IRQHandler(void)
{
    uint8_t res;
    
    if (GPS_USART->ISR & (1 << 5))  /* 接收到数据 */
    {
        res = GPS_USART->RDR;  /* 读取接收到的数据 */
        
        /* 将接收到的数据存入缓冲区 */
        if (gps_rx_count < GPS_RX_BUFFER_SIZE - 1)
        {
            gps_rx_buffer[gps_rx_count++] = res;
            
            /* 如果收到换行符，表示一条NMEA语句接收完成 */
            if (res == '\n')
            {
                /* 确保字符串以NULL结尾 */
                gps_rx_buffer[gps_rx_count] = '\0';
            }
        }
        else
        {
            /* 缓冲区溢出，重置缓冲区 */
            gps_rx_count = 0;
        }
    }
}

/**
 * @brief       计算NMEA校验和
 * @param       sentence: NMEA语句
 * @param       len: 语句长度
 * @retval      校验和
 */
static uint8_t gps_calc_checksum(const char *sentence, uint16_t len)
{
    uint8_t checksum = 0;
    uint16_t i;
    
    /* 校验和计算：从$后的第一个字符开始，到*前的所有字符进行异或 */
    for (i = 1; i < len && sentence[i] != '*' && sentence[i] != '\r' && sentence[i] != '\n'; i++)
    {
        checksum ^= sentence[i];
    }
    
    return checksum;
}

/**
 * @brief       验证NMEA语句校验和
 * @param       sentence: NMEA语句
 * @retval      1: 校验通过, 0: 校验失败
 */
static uint8_t gps_verify_checksum(const char *sentence)
{
    uint8_t calculated_checksum;
    uint8_t provided_checksum = 0;
    uint16_t len = strlen(sentence);
    uint16_t i;
    
    /* 查找校验和位置 */
    for (i = 0; i < len; i++)
    {
        if (sentence[i] == '*')
        {
            /* 将校验和从十六进制字符串转换为数值 */
            if (i + 2 < len)
            {
                char checksum_str[3] = {sentence[i+1], sentence[i+2], '\0'};
                provided_checksum = strtol(checksum_str, NULL, 16);
                break;
            }
            else
            {
                return 0;  /* 校验和格式错误 */
            }
        }
    }
    
    if (i == len)
    {
        return 0;  /* 未找到校验和 */
    }
    
    calculated_checksum = gps_calc_checksum(sentence, len);
    
    return (calculated_checksum == provided_checksum);
}

/**
 * @brief       将NMEA格式的坐标转换为度
 * @param       coord_str: NMEA格式的坐标字符串 (ddmm.mmmm)
 * @param       is_lon: 1表示经度, 0表示纬度
 * @retval      转换后的坐标，单位为度
 */
static float gps_nmea_to_decimal(const char *coord_str, uint8_t is_lon)
{
    float coord = atof(coord_str);
    int degrees = (int)(coord / 100);  /* 经度和纬度处理方式相同 */
    float minutes = coord - degrees * 100;
    return degrees + minutes / 60.0f;
}

/**
 * @brief       解析GPRMC语句
 * @param       rmc_str: GPRMC语句字符串
 * @retval      无
 */
static void gps_parse_rmc(const char *rmc_str)
{
    char *token;
    char *save_ptr;
    char rmc_copy[GPS_RX_BUFFER_SIZE];
    uint8_t token_count = 0;
    
    /* 复制字符串，因为strtok会修改原字符串 */
    strncpy(rmc_copy, rmc_str, GPS_RX_BUFFER_SIZE - 1);
    rmc_copy[GPS_RX_BUFFER_SIZE - 1] = '\0';
    
    /* 分割GPRMC语句 */
    token = strtok_r(rmc_copy, ",", &save_ptr);
    
    while (token != NULL)
    {
        token_count++;
        
        switch (token_count)
        {
            case 2:  /* UTC时间 */
                /* 可以在这里解析时间，如果需要 */
                break;
                
            case 3:  /* 定位状态 A=有效, V=无效 */
                if (*token == 'A')
                {
                    current_location.fixed = 1;
                }
                else
                {
                    current_location.fixed = 0;
                    return;  /* 如果定位无效，直接返回 */
                }
                break;
                
            case 4:  /* 纬度 */
                if (strlen(token) > 0)
                {
                    current_location.latitude = gps_nmea_to_decimal(token, 0);
                }
                break;
                
            case 5:  /* 纬度半球 N=北, S=南 */
                if (*token == 'S')
                {
                    current_location.latitude = -current_location.latitude;
                }
                break;
                
            case 6:  /* 经度 */
                if (strlen(token) > 0)
                {
                    current_location.longitude = gps_nmea_to_decimal(token, 1);
                }
                break;
                
            case 7:  /* 经度半球 E=东, W=西 */
                if (*token == 'W')
                {
                    current_location.longitude = -current_location.longitude;
                }
                break;
                
            default:
                break;
        }
        
        token = strtok_r(NULL, ",", &save_ptr);
    }
}

/**
 * @brief       解析GPGGA语句
 * @param       gga_str: GPGGA语句字符串
 * @retval      无
 */
static void gps_parse_gga(const char *gga_str)
{
    char *token;
    char *save_ptr;
    char gga_copy[GPS_RX_BUFFER_SIZE];
    uint8_t token_count = 0;
    
    /* 复制字符串，因为strtok会修改原字符串 */
    strncpy(gga_copy, gga_str, GPS_RX_BUFFER_SIZE - 1);
    gga_copy[GPS_RX_BUFFER_SIZE - 1] = '\0';
    
    /* 分割GPGGA语句 */
    token = strtok_r(gga_copy, ",", &save_ptr);
    
    while (token != NULL)
    {
        token_count++;
        
        switch (token_count)
        {
            case 2:  /* UTC时间 */
                /* 可以在这里解析时间，如果需要 */
                break;
                
            case 3:  /* 纬度 */
                if (strlen(token) > 0)
                {
                    current_location.latitude = gps_nmea_to_decimal(token, 0);
                }
                break;
                
            case 4:  /* 纬度半球 N=北, S=南 */
                if (*token == 'S')
                {
                    current_location.latitude = -current_location.latitude;
                }
                break;
                
            case 5:  /* 经度 */
                if (strlen(token) > 0)
                {
                    current_location.longitude = gps_nmea_to_decimal(token, 1);
                }
                break;
                
            case 6:  /* 经度半球 E=东, W=西 */
                if (*token == 'W')
                {
                    current_location.longitude = -current_location.longitude;
                }
                break;
                
            case 7:  /* 定位质量指示 0=无效, 1=GPS定位, 2=差分GPS */
                if (*token == '1' || *token == '2')
                {
                    current_location.fixed = 1;
                }
                else
                {
                    current_location.fixed = 0;
                }
                break;
                
            default:
                break;
        }
        
        token = strtok_r(NULL, ",", &save_ptr);
    }
}

/**
 * @brief       解析GPS数据，提取位置信息
 * @param       无
 * @retval      无
 */
static void gps_parse_data(void)
{
    char *nmea_start;
    char *nmea_end;
    char nmea_sentence[GPS_RX_BUFFER_SIZE];
    uint16_t sentence_len;
    
    /* 在缓冲区中查找NMEA语句 */
    nmea_start = strstr((char *)gps_rx_buffer, "$GP");
    
    while (nmea_start != NULL)
    {
        /* 查找语句结束符 */
        nmea_end = strstr(nmea_start, "\r\n");
        
        if (nmea_end != NULL)
        {
            /* 计算语句长度并复制到临时缓冲区 */
            sentence_len = nmea_end - nmea_start + 2;  /* 包括\r\n */
            if (sentence_len < GPS_RX_BUFFER_SIZE)
            {
                memcpy(nmea_sentence, nmea_start, sentence_len);
                nmea_sentence[sentence_len] = '\0';
                
                /* 验证校验和 */
                if (gps_verify_checksum(nmea_sentence))
                {
                    /* 根据语句类型进行解析 */
                    if (strncmp(nmea_sentence, "$GPRMC", 6) == 0)
                    {
                        gps_parse_rmc(nmea_sentence);
                    }
                    else if (strncmp(nmea_sentence, "$GPGGA", 6) == 0)
                    {
                        gps_parse_gga(nmea_sentence);
                    }
                }
            }
            
            /* 继续查找下一个NMEA语句 */
            nmea_start = strstr(nmea_end + 2, "$GP");
        }
        else
        {
            break;  /* 未找到完整的语句 */
        }
    }
}

/**
 * @brief       更新GPS数据
 * @param       无
 * @retval      无
 */
void gps_update(void)
{
    /* 解析接收到的GPS数据 */
    if (gps_rx_count > 0)
    {
        gps_parse_data();
        
        /* 清空缓冲区 */
        memset(gps_rx_buffer, 0, GPS_RX_BUFFER_SIZE);
        gps_rx_count = 0;
    }
}

/**
 * @brief       获取当前位置信息
 * @param       无
 * @retval      当前位置信息
 */
gps_location_t gps_get_location(void)
{
    return current_location;
} 

