#ifndef __GPS_H
#define __GPS_H

#include "sys.h"
#include "pinconfig.h"  /* 包含引脚配置 */

/* GPS位置信息结构体 */
typedef struct
{
    float latitude;    /* 纬度值，单位：度 */
    float longitude;   /* 经度值，单位：度 */
    uint8_t fixed;     /* 定位状态，1:定位成功，0:未定位 */
} gps_location_t;

/* GPS控制函数 */
void gps_init(void);                     /* 初始化GPS模块 */
void gps_update(void);                   /* 更新GPS数据 */
gps_location_t gps_get_location(void);   /* 获取当前位置信息 */

#endif /* __GPS_H */ 

