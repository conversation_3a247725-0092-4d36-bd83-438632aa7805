#include "i2c.h"
#include "sys.h"

/* 使用外部HAL_GetTick函数 */
extern uint32_t HAL_GetTick(void);

/**
 * @brief 获取系统时间戳(毫秒)
 * @param 无
 * @retval 时间戳，单位ms
 */
uint32_t sys_get_tick_ms(void)
{
    return HAL_GetTick();
}

/**
 * @brief       I2C初始化
 * @param       i2c: I2C控制器，I2C1-I2C4
 * @param       speed: 通信速度，单位Hz
 * @retval      无
 */
void i2c_init(I2C_TypeDef *i2c, uint32_t speed)
{
    uint32_t timing;
    
    /* 使能I2C时钟 */
    if (i2c == I2C1)
    {
        RCC->APB1LENR |= (1 << 21);  /* 使能I2C1时钟 */
    }
    else if (i2c == I2C2)
    {
        RCC->APB1LENR |= (1 << 22);  /* 使能I2C2时钟 */
    }
    else if (i2c == I2C3)
    {
        RCC->APB1LENR |= (1 << 23);  /* 使能I2C3时钟 */
    }
    else if (i2c == I2C4)
    {
        RCC->APB4ENR |= (1 << 7);    /* 使能I2C4时钟 */
    }
    
    /* 复位I2C */
    i2c->CR1 = 0;
    
    /* 计算时序参数 */
    if (speed <= 100000)        /* 标准模式: <= 100KHz */
    {
        /* 配置时序参数 (PRESC=7, SCLDEL=3, SDADEL=1, SCLH=39, SCLL=40) */
        timing = 0x70731427;
    }
    else if (speed <= 400000)   /* 快速模式: <= 400KHz */
    {
        /* 配置时序参数 (PRESC=3, SCLDEL=1, SDADEL=1, SCLH=9, SCLL=20) */
        timing = 0x30911920;
    }
    else                        /* 快速模式Plus: <= 1MHz */
    {
        /* 配置时序参数 (PRESC=1, SCLDEL=0, SDADEL=0, SCLH=3, SCLL=9) */
        timing = 0x10600309;
    }
    
    /* 配置I2C */
    i2c->TIMINGR = timing;      /* 设置时序 */
    i2c->CR1 |= (1 << 0);       /* 使能I2C */
}

/**
 * @brief       等待I2C就绪
 * @param       i2c: I2C控制器
 * @retval      状态: 0=成功, 1=错误, 3=超时
 */
static uint8_t i2c_wait_ready(I2C_TypeDef *i2c)
{
    uint32_t start_time = sys_get_tick_ms();
    
    /* 等待I2C不忙 */
    while (i2c->ISR & (1 << 15))  /* BUSY标志 */
    {
        if (sys_get_tick_ms() - start_time > I2C_TIMEOUT_MS)
        {
            return I2C_TIMEOUT;  /* 超时 */
        }
    }
    
    return I2C_OK;
}

/**
 * @brief       等待特定事件
 * @param       i2c: I2C控制器
 * @param       event: 要等待的事件标志位
 * @retval      状态: 0=成功, 1=错误, 3=超时
 */
static uint8_t i2c_wait_event(I2C_TypeDef *i2c, uint32_t event)
{
    uint32_t start_time = sys_get_tick_ms();
    
    /* 等待事件发生 */
    while (!(i2c->ISR & event))
    {
        /* 检查NACK */
        if (i2c->ISR & (1 << 4))  /* NACKF标志 */
        {
            i2c->ICR |= (1 << 4);  /* 清除NACKF标志 */
            return I2C_NACK;       /* NACK错误 */
        }
        
        /* 检查超时 */
        if (sys_get_tick_ms() - start_time > I2C_TIMEOUT_MS)
        {
            return I2C_TIMEOUT;    /* 超时 */
        }
    }
    
    return I2C_OK;
}

/**
 * @brief       I2C写数据
 * @param       i2c: I2C控制器
 * @param       dev_addr: 设备地址
 * @param       data: 数据缓冲区
 * @param       len: 数据长度
 * @retval      状态: 0=成功, 其他=错误码
 */
uint8_t i2c_write(I2C_TypeDef *i2c, uint8_t dev_addr, uint8_t *data, uint16_t len)
{
    uint8_t status;
    uint16_t i;
    
    /* 等待I2C就绪 */
    status = i2c_wait_ready(i2c);
    if (status != I2C_OK)
    {
        return status;
    }
    
    /* 配置传输参数 */
    i2c->CR2 = 0;
    i2c->CR2 |= ((uint32_t)dev_addr << 1);  /* 设备地址 */
    i2c->CR2 |= ((uint32_t)len << 16);      /* 数据长度 */
    i2c->CR2 |= (1 << 13);                  /* 生成停止位 */
    i2c->CR2 |= (1 << 25);                  /* 自动结束模式 */
    
    /* 开始传输 */
    i2c->CR2 |= (1 << 10);                  /* 开始位 */
    
    /* 发送数据 */
    for (i = 0; i < len; i++)
    {
        /* 等待发送缓冲区空 */
        status = i2c_wait_event(i2c, (1 << 1));  /* TXIS标志 */
        if (status != I2C_OK)
        {
            return status;
        }
        
        /* 写入数据 */
        i2c->TXDR = data[i];
    }
    
    /* 等待传输完成 */
    status = i2c_wait_event(i2c, (1 << 6));  /* TC标志 */
    if (status != I2C_OK)
    {
        return status;
    }
    
    return I2C_OK;
}

/**
 * @brief       I2C读数据
 * @param       i2c: I2C控制器
 * @param       dev_addr: 设备地址
 * @param       data: 数据缓冲区
 * @param       len: 数据长度
 * @retval      状态: 0=成功, 其他=错误码
 */
uint8_t i2c_read(I2C_TypeDef *i2c, uint8_t dev_addr, uint8_t *data, uint16_t len)
{
    uint8_t status;
    uint16_t i;
    
    /* 等待I2C就绪 */
    status = i2c_wait_ready(i2c);
    if (status != I2C_OK)
    {
        return status;
    }
    
    /* 配置传输参数 */
    i2c->CR2 = 0;
    i2c->CR2 |= ((uint32_t)dev_addr << 1);  /* 设备地址 */
    i2c->CR2 |= ((uint32_t)len << 16);      /* 数据长度 */
    i2c->CR2 |= (1 << 10);                  /* 读取方向 */
    i2c->CR2 |= (1 << 13);                  /* 生成停止位 */
    i2c->CR2 |= (1 << 25);                  /* 自动结束模式 */
    
    /* 开始传输 */
    i2c->CR2 |= (1 << 13);                  /* 开始位 */
    
    /* 接收数据 */
    for (i = 0; i < len; i++)
    {
        /* 等待接收缓冲区非空 */
        status = i2c_wait_event(i2c, (1 << 2));  /* RXNE标志 */
        if (status != I2C_OK)
        {
            return status;
        }
        
        /* 读取数据 */
        data[i] = i2c->RXDR;
    }
    
    return I2C_OK;
}

/**
 * @brief       I2C写寄存器
 * @param       i2c: I2C控制器
 * @param       dev_addr: 设备地址
 * @param       reg: 寄存器地址
 * @param       data: 数据
 * @retval      状态: 0=成功, 其他=错误码
 */
uint8_t i2c_write_reg(I2C_TypeDef *i2c, uint8_t dev_addr, uint8_t reg, uint8_t data)
{
    uint8_t buf[2];
    
    buf[0] = reg;
    buf[1] = data;
    
    return i2c_write(i2c, dev_addr, buf, 2);
}

/**
 * @brief       I2C读寄存器
 * @param       i2c: I2C控制器
 * @param       dev_addr: 设备地址
 * @param       reg: 寄存器地址
 * @param       data: 数据指针
 * @retval      状态: 0=成功, 其他=错误码
 */
uint8_t i2c_read_reg(I2C_TypeDef *i2c, uint8_t dev_addr, uint8_t reg, uint8_t *data)
{
    uint8_t status;
    
    /* 写入寄存器地址 */
    status = i2c_write(i2c, dev_addr, &reg, 1);
    if (status != I2C_OK)
    {
        return status;
    }
    
    /* 读取数据 */
    return i2c_read(i2c, dev_addr, data, 1);
}

/**
 * @brief       I2C写多个寄存器
 * @param       i2c: I2C控制器
 * @param       dev_addr: 设备地址
 * @param       reg: 起始寄存器地址
 * @param       data: 数据缓冲区
 * @param       len: 数据长度
 * @retval      状态: 0=成功, 其他=错误码
 */
uint8_t i2c_write_multi_reg(I2C_TypeDef *i2c, uint8_t dev_addr, uint8_t reg, uint8_t *data, uint16_t len)
{
    uint8_t status;
    uint8_t *buf;
    uint16_t i;
    
    /* 分配缓冲区 */
    buf = (uint8_t *)malloc(len + 1);
    if (buf == NULL)
    {
        return I2C_ERROR;
    }
    
    /* 准备数据 */
    buf[0] = reg;
    for (i = 0; i < len; i++)
    {
        buf[i + 1] = data[i];
    }
    
    /* 发送数据 */
    status = i2c_write(i2c, dev_addr, buf, len + 1);
    
    /* 释放缓冲区 */
    free(buf);
    
    return status;
}

/**
 * @brief       I2C读多个寄存器
 * @param       i2c: I2C控制器
 * @param       dev_addr: 设备地址
 * @param       reg: 起始寄存器地址
 * @param       data: 数据缓冲区
 * @param       len: 数据长度
 * @retval      状态: 0=成功, 其他=错误码
 */
uint8_t i2c_read_multi_reg(I2C_TypeDef *i2c, uint8_t dev_addr, uint8_t reg, uint8_t *data, uint16_t len)
{
    uint8_t status;
    
    /* 写入寄存器地址 */
    status = i2c_write(i2c, dev_addr, &reg, 1);
    if (status != I2C_OK)
    {
        return status;
    }
    
    /* 读取数据 */
    return i2c_read(i2c, dev_addr, data, len);
}

