#ifndef __I2C_H
#define __I2C_H

#include <stdlib.h>  /* 包含NULL定义及malloc/free函数声明 */
#include <stdint.h>
#include "../Drivers/CMSIS/Device/ST/STM32H7xx/Include/stm32h7xx.h"      /* 包含标准STM32H7库头文件 */ 
#include "sys.h"

/* I2C通信状态定义 */
#define I2C_OK                  0
#define I2C_ERROR               1
#define I2C_BUSY                2
#define I2C_TIMEOUT             3
#define I2C_NACK                4

/* I2C超时定义 */
#define I2C_TIMEOUT_MS          100  /* 通信超时时间 */

/* I2C初始化函数 */
void i2c_init(I2C_TypeDef *i2c, uint32_t speed);

/* I2C通信函数 */
uint8_t i2c_write(I2C_TypeDef *i2c, uint8_t dev_addr, uint8_t *data, uint16_t len);
uint8_t i2c_read(I2C_TypeDef *i2c, uint8_t dev_addr, uint8_t *data, uint16_t len);
uint8_t i2c_write_reg(I2C_TypeDef *i2c, uint8_t dev_addr, uint8_t reg, uint8_t data);
uint8_t i2c_read_reg(I2C_TypeDef *i2c, uint8_t dev_addr, uint8_t reg, uint8_t *data);
uint8_t i2c_write_multi_reg(I2C_TypeDef *i2c, uint8_t dev_addr, uint8_t reg, uint8_t *data, uint16_t len);
uint8_t i2c_read_multi_reg(I2C_TypeDef *i2c, uint8_t dev_addr, uint8_t reg, uint8_t *data, uint16_t len);

/* OLED专用I2C初始化 */
void i2c_init_oled(void);

#endif /* __I2C_H */

