#include "sys.h"
#include "../Drivers/SYSTEM/usart/usart.h"
#include "delay.h"
#include <stdio.h>
#include <stdlib.h>

#include "motor.h"
#include "servo.h"
#include "vl53l0x.h"
#include "gps.h"
#include "esp8266.h"
#include "v831_camera.h"
#include "oled.h"

static volatile uint32_t g_systick_count = 0;

void SysTick_Handler(void)
{
    g_systick_count++;
}
uint32_t HAL_GetTick(void)
{
    return g_systick_count;
}

static void systick_init(uint32_t sysclk_mhz)
{
    SysTick->LOAD = sysclk_mhz * 1000 - 1;
    SysTick->VAL = 0;
    SysTick->CTRL = SysTick_CTRL_CLKSOURCE_Msk | SysTick_CTRL_TICKINT_Msk | SysTick_CTRL_ENABLE_Msk;
}

/* 看门狗相关定义 */
#define WATCHDOG_TIMEOUT_MS  1000
#define ENABLE_WATCHDOG      0

#if ENABLE_WATCHDOG
static void iwdg_init(uint32_t timeout_ms)
{
    RCC->CSR |= RCC_CSR_LSION;
    while(!(RCC->CSR & RCC_CSR_LSIRDY));

    IWDG->PR = 0;
    uint32_t reload = (timeout_ms * 8) - 1;
    if (reload > 0xFFF) reload = 0xFFF;
    IWDG->RLR = reload;
    while (IWDG->SR & (IWDG_SR_PVU | IWDG_SR_RVU));
    IWDG->KR = 0xCCCC;
}

static void iwdg_feed(void)
{
    IWDG->KR = 0xAAAA;
}
#else
static void iwdg_init(uint32_t timeout_ms)
{
    (void)timeout_ms;
}

static void iwdg_feed(void)
{
}
#endif

typedef enum {
    SYS_STATE_INIT = 0,
    SYS_STATE_CLAMP_WAITING,
    SYS_STATE_RUNNING,
    SYS_STATE_DEFECT_FOUND,
    SYS_STATE_DEFECT_WAITING,  /* 新增：缺陷检测后等待状态 */
    SYS_STATE_ERROR
} system_state_t;

static system_state_t g_sys_state = SYS_STATE_INIT;
static uint32_t g_last_state_time = 0;
static uint32_t g_defect_count = 0;
static uint8_t g_error_count = 0;

static void state_change(system_state_t new_state)
{
    g_last_state_time = HAL_GetTick();
    
    g_sys_state = new_state;
}

static uint8_t system_init(void)
{
    uint8_t init_success = 1;
    
    motor_init();
    servo_init();
    vl53l0x_init();
    gps_init();
    v831_init();

    /* 初始化ESP8266 WiFi模块 */
    esp8266_init();
    if (esp8266_connect_ap())
    {
        if (!esp8266_connect_mqtt())
        {
            delay_ms(1000);
            if (!esp8266_connect_mqtt())
            {
                /* MQTT连接失败不影响基本功能，继续运行 */
            }
        }
    }
    else
    {
        /* WiFi连接失败不影响基本功能，继续运行 */
    }
    
    oled_init();
    
    return init_success;
}

static void state_machine_process(void)
{
    uint32_t current_time = HAL_GetTick();
    defect_info_t defect;
    gps_location_t location;
    uint32_t elapsed_time, remaining_time;  /* 用于缺陷等待状态 */
    switch (g_sys_state)
    {
        case SYS_STATE_INIT:
            if (system_init())
            {
                oled_clear();
                oled_set_cursor(0, 0);
                oled_printf("Rail Defect Detector");
                oled_set_cursor(0, 1);
                oled_printf("Init Complete");
                oled_set_cursor(0, 2);
                /* 显示WiFi连接状态 */
                if (esp8266_get_error() == ESP8266_ERROR_NONE)
                {
                    oled_printf("WiFi: Connected");
                }
                else
                {
                    oled_printf("WiFi: Offline mode");
                }
                oled_set_cursor(0, 3);
                oled_printf("Waiting for clamp...");

                servo_clamp_open();

                state_change(SYS_STATE_CLAMP_WAITING);
            }
            else
            {
                state_change(SYS_STATE_ERROR);
            }
            break;
        
        case SYS_STATE_CLAMP_WAITING:
            if (vl53l0x_check_clamp())
            {
                servo_clamp_close();
                
                delay_ms(500);
                
                oled_clear();
                oled_set_cursor(0, 0);
                oled_printf("Start Moving");
                oled_set_cursor(0, 1);
                oled_printf("Detecting Defects...");

                motor_start();
                
                state_change(SYS_STATE_RUNNING);
            }
            else if (current_time - g_last_state_time > 30000)
            {
                oled_set_cursor(0, 3);
                oled_printf("Waiting: %ds", (current_time - g_last_state_time) / 1000);
                
                g_last_state_time = current_time;
            }
            break;
        
        case SYS_STATE_RUNNING:
            gps_update();
            v831_update();

            /* MQTT心跳维护 - 每60秒发送一次 */
            static uint32_t last_ping_time = 0;
            if (esp8266_is_mqtt_connected() && (current_time - last_ping_time > 60000))
            {
                esp8266_mqtt_ping();
                last_ping_time = current_time;
            }

            defect = v831_get_defect_info();
            if (defect.detected && defect.confidence >= DEFECT_THRESHOLD)
            {
                /* 检查是否为需要停止的关键缺陷（压伤或损伤） */
                if (v831_is_critical_defect(defect.type))
                {
                    /* 检测到关键缺陷，立即制动停止电机 */
                    motor_stop();

                    /* 显示紧急停止信息 */
                    oled_clear();
                    oled_set_cursor(0, 0);
                    oled_printf("CRITICAL DEFECT!");
                    oled_set_cursor(0, 1);
                    if (defect.type == DEFECT_TYPE_YASHANG)
                        oled_printf("Type: YASHANG");
                    else if (defect.type == DEFECT_TYPE_SUNSHANG)
                        oled_printf("Type: SUNSHANG");
                    oled_set_cursor(0, 2);
                    oled_printf("Conf: %d%% - STOP", defect.confidence);

                    state_change(SYS_STATE_DEFECT_FOUND);
                    g_defect_count++;
                }
                else
                {
                    /* 检测到非关键缺陷，记录但不停止 */
                    static uint32_t last_log_time = 0;
                    uint32_t current_time = HAL_GetTick();

                    /* 每5秒记录一次非关键缺陷 */
                    if (current_time - last_log_time > 5000)
                    {
                        gps_location_t location = gps_get_location();
                        if (location.fixed)
                        {
                            /* 上传非关键缺陷数据，但不停止运行 */
                            esp8266_mqtt_publish_defect(
                                location.latitude,
                                location.longitude,
                                defect.type,
                                defect.confidence,
                                0  /* 非关键缺陷计数为0 */
                            );
                        }

                        /* 在OLED上简单显示，不影响正常运行 */
                        oled_set_cursor(0, 3);
                        oled_printf("Minor: T%d C%d%%", defect.type, defect.confidence);

                        last_log_time = current_time;
                    }
                }
            }

            if (current_time - g_last_state_time > 5000)
            {
                g_last_state_time = current_time;
            }
            break;
        
        case SYS_STATE_DEFECT_FOUND:
            location = gps_get_location();
            defect = v831_get_defect_info();

            if (location.fixed)
            {
                /* 尝试上传详细缺陷数据到云端 */
                uint8_t upload_success = esp8266_mqtt_publish_defect(
                    location.latitude,
                    location.longitude,
                    defect.type,
                    defect.confidence,
                    g_defect_count
                );

                oled_clear();
                oled_set_cursor(0, 0);
                oled_printf("Critical #%d Found!", g_defect_count);
                oled_set_cursor(0, 1);
                if (defect.type == DEFECT_TYPE_YASHANG)
                    oled_printf("YASHANG Conf:%d%%", defect.confidence);
                else if (defect.type == DEFECT_TYPE_SUNSHANG)
                    oled_printf("SUNSHANG Conf:%d%%", defect.confidence);
                else
                    oled_printf("Type:%d Conf:%d%%", defect.type, defect.confidence);
                oled_set_cursor(0, 2);
                oled_printf("Lat:%.4f Lon:%.4f", location.latitude, location.longitude);
                oled_set_cursor(0, 3);
                if (upload_success)
                {
                    oled_printf("Status: Uploaded");
                }
                else
                {
                    oled_printf("Status: Local saved");
                }

                /* 进入等待状态，停留5秒 */
                state_change(SYS_STATE_DEFECT_WAITING);
            }
            else
            {
                /* 没有GPS定位，显示等待GPS信息 */
                oled_clear();
                oled_set_cursor(0, 0);
                oled_printf("Critical #%d Found!", g_defect_count);
                oled_set_cursor(0, 1);
                if (defect.type == DEFECT_TYPE_YASHANG)
                    oled_printf("YASHANG Conf:%d%%", defect.confidence);
                else if (defect.type == DEFECT_TYPE_SUNSHANG)
                    oled_printf("SUNSHANG Conf:%d%%", defect.confidence);
                else
                    oled_printf("Type:%d Conf:%d%%", defect.type, defect.confidence);
                oled_set_cursor(0, 2);
                oled_printf("Waiting for GPS...");
                oled_set_cursor(0, 3);
                oled_printf("No location data");

                /* 等待5秒后继续运行 */
                if (current_time - g_last_state_time > 5000)
                {
                    state_change(SYS_STATE_DEFECT_WAITING);
                }
            }
            break;

        case SYS_STATE_DEFECT_WAITING:
            /* 显示等待倒计时 */
            elapsed_time = current_time - g_last_state_time;
            remaining_time = 5000 - elapsed_time;  /* 5秒 = 5000毫秒 */

            if (remaining_time > 5000)  /* 防止溢出 */
            {
                remaining_time = 0;
            }

            /* 更新显示 - 每秒更新一次倒计时 */
            if (elapsed_time % 1000 < 100)  /* 每秒更新一次显示 */
            {
                oled_clear();
                oled_set_cursor(0, 0);
                oled_printf("MOTOR STOPPED");
                oled_set_cursor(0, 1);
                oled_printf("Defect Processing");
                oled_set_cursor(0, 2);
                oled_printf("Resume in: %ds", (remaining_time / 1000) + 1);
                oled_set_cursor(0, 3);
                oled_printf("Please wait...");
            }

            /* 检查是否等待时间到 */
            if (elapsed_time >= 5000)  /* 5秒后继续运行 */
            {
                oled_clear();
                oled_set_cursor(0, 0);
                oled_printf("Resuming Detection");
                oled_set_cursor(0, 1);
                oled_printf("Motor Starting...");

                /* 重新启动电机 */
                motor_start();

                delay_ms(1000);  /* 显示1秒启动信息 */
                state_change(SYS_STATE_RUNNING);
            }
            break;
        
        case SYS_STATE_ERROR:
            g_error_count++;
            
            oled_clear();
            oled_set_cursor(0, 0);
            oled_printf("System Error!");
            oled_set_cursor(0, 1);
            oled_printf("Error Count: %d", g_error_count);
            oled_set_cursor(0, 2);
            oled_printf("Trying restart...");
            
            if (g_error_count >= 3)
            {
                g_error_count = 0;
                state_change(SYS_STATE_INIT);
            }
            else
            {
                delay_ms(1000);
            }
            break;
        
        default:
            state_change(SYS_STATE_INIT);
            break;
    }
}

int main(void)
{
    sys_stm32_clock_init(104, 5, 1, 2);
    systick_init(520);
    delay_init(520);
    usart_init(130, 115200);
	oled_init();
    iwdg_init(WATCHDOG_TIMEOUT_MS);
	

    g_sys_state = SYS_STATE_INIT;
    g_last_state_time = HAL_GetTick();

    while (1)
    {
        state_machine_process();
        iwdg_feed();
        delay_ms(10);
    }
}

void switch_usart1_device(uint8_t device)
{
    if (device == 0)
    {
        sys_gpio_set(V831_TX_GPIO_PORT, 1 << V831_TX_PIN, 
                     SYS_GPIO_MODE_AF, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);
        
        sys_gpio_set(V831_RX_GPIO_PORT, 1 << V831_RX_PIN, 
                     SYS_GPIO_MODE_AF, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);
        
        sys_gpio_af_set(V831_TX_GPIO_PORT, V831_TX_PIN, 7);
        sys_gpio_af_set(V831_RX_GPIO_PORT, V831_RX_PIN, 7);
        
        extern void v831_rx_callback(uint8_t data);
        usart_register_rx_callback(v831_rx_callback);
    }
    else
    {
        sys_gpio_set(ESP8266_TX_GPIO_PORT, 1 << ESP8266_TX_PIN, 
                     SYS_GPIO_MODE_AF, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);
        
        sys_gpio_set(ESP8266_RX_GPIO_PORT, 1 << ESP8266_RX_PIN, 
                     SYS_GPIO_MODE_AF, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);
        
        sys_gpio_af_set(ESP8266_TX_GPIO_PORT, ESP8266_TX_PIN, 7);
        sys_gpio_af_set(ESP8266_RX_GPIO_PORT, ESP8266_RX_PIN, 7);
        
        extern void esp8266_rx_callback(uint8_t data);
        usart_register_rx_callback(esp8266_rx_callback);
    }
}
