#include "sys.h"
#include "delay.h"
#include "motor.h"

/* 当前电机状态 */
static motor_state_t g_motor_state = MOTOR_STATE_STOP;



void motor_init(void)
{
    /* 使能GPIO时钟 */
    RCC->AHB4ENR |= 1 << 2;    /* PC时钟使能 */

    /* 配置电机供电控制引脚为输出模式 */
    sys_gpio_set(MOTOR_POWER_GPIO, MOTOR_POWER_PIN,
                 SYS_GPIO_MODE_OUT, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_MID, SYS_GPIO_PUPD_PU);

    /* 初始状态：电机停止（断电） */
    motor_stop();

    /* 初始化全局变量 */
    g_motor_state = MOTOR_STATE_STOP;
}

void motor_start(void)
{
    /* 直接供电启动电机 */
    sys_gpio_pin_set(MOTOR_POWER_GPIO, MOTOR_POWER_PIN, 1);

    /* 更新电机状态 */
    g_motor_state = MOTOR_STATE_RUNNING;
}

void motor_stop(void)
{
    /* 断电停止电机 */
    sys_gpio_pin_set(MOTOR_POWER_GPIO, MOTOR_POWER_PIN, 0);

    /* 更新电机状态 */
    g_motor_state = MOTOR_STATE_STOP;
}

motor_state_t motor_get_state(void)
{
    return g_motor_state;
}


