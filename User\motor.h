#ifndef __MOTOR_H
#define __MOTOR_H

/* 包含头文件 */
#include "sys.h"

/* 直接供电控制引脚定义 */
#define MOTOR_POWER_GPIO       GPIOC        /* 电机供电控制引脚 */
#define MOTOR_POWER_PIN        SYS_GPIO_PIN8

/**
 * @brief  电机运行状态定义
 */
typedef enum
{
    MOTOR_STATE_RUNNING = 0,    /* 电机运行状态 */
    MOTOR_STATE_STOP = 1        /* 电机停止状态 */
} motor_state_t;


void motor_init(void);                      /* 电机初始化 */
void motor_start(void);                     /* 启动电机（直接供电） */
void motor_stop(void);                      /* 停止电机（断电） */
motor_state_t motor_get_state(void);        /* 获取电机当前状态 */

#endif /* __MOTOR_H */

