/**
 * @file mqtt_test.c
 * @brief MQTT连接测试程序
 * <AUTHOR> Assistant
 * @date 2025-07-24
 */

#include "sys.h"
#include "delay.h"
#include "esp8266.h"
#include "gps.h"
#include "v831_camera.h"
#include "oled.h"

/**
 * @brief MQTT连接测试函数
 * @retval 1-测试成功，0-测试失败
 */
uint8_t mqtt_connection_test(void)
{
    uint8_t test_result = 0;
    uint8_t wifi_connected = 0;
    uint8_t mqtt_connected = 0;
    
    /* 显示测试开始信息 */
    oled_clear();
    oled_set_cursor(0, 0);
    oled_printf("MQTT Test Starting...");
    
    /* 1. 测试WiFi连接 */
    oled_set_cursor(0, 1);
    oled_printf("WiFi: Connecting...");
    
    wifi_connected = esp8266_connect_ap();
    if (wifi_connected)
    {
        oled_set_cursor(0, 1);
        oled_printf("WiFi: Connected OK");
        delay_ms(1000);
        
        /* 2. 测试MQTT连接 */
        oled_set_cursor(0, 2);
        oled_printf("MQTT: Connecting...");
        
        mqtt_connected = esp8266_connect_mqtt();
        if (mqtt_connected)
        {
            oled_set_cursor(0, 2);
            oled_printf("MQTT: Connected OK");
            
            /* 3. 测试数据发送 */
            oled_set_cursor(0, 3);
            oled_printf("Data: Testing...");
            delay_ms(1000);
            
            /* 发送测试数据 */
            uint8_t send_result = esp8266_mqtt_publish_defect(
                31.230416,  /* 测试纬度 - 上海 */
                121.473701, /* 测试经度 - 上海 */
                DEFECT_TYPE_YASHANG,  /* 测试缺陷类型 */
                85,         /* 测试置信度 */
                1           /* 测试缺陷计数 */
            );
            
            if (send_result)
            {
                oled_set_cursor(0, 3);
                oled_printf("Data: Sent OK");
                test_result = 1;
            }
            else
            {
                oled_set_cursor(0, 3);
                oled_printf("Data: Send Failed");
            }
        }
        else
        {
            oled_set_cursor(0, 2);
            oled_printf("MQTT: Failed");
            oled_set_cursor(0, 3);
            oled_printf("Check parameters");
        }
    }
    else
    {
        oled_set_cursor(0, 1);
        oled_printf("WiFi: Failed");
        oled_set_cursor(0, 2);
        oled_printf("Check SSID/Password");
    }
    
    return test_result;
}

/**
 * @brief GPS测试函数
 * @retval 1-GPS定位成功，0-GPS定位失败
 */
uint8_t gps_test(void)
{
    uint8_t test_count = 0;
    gps_location_t location;
    
    oled_clear();
    oled_set_cursor(0, 0);
    oled_printf("GPS Test Starting...");
    
    /* 等待GPS定位，最多等待30秒 */
    while (test_count < 30)
    {
        gps_update();
        location = gps_get_location();
        
        oled_set_cursor(0, 1);
        oled_printf("GPS: Waiting... %ds", test_count);
        
        if (location.fixed)
        {
            oled_clear();
            oled_set_cursor(0, 0);
            oled_printf("GPS: Fixed OK");
            oled_set_cursor(0, 1);
            oled_printf("Lat: %.6f", location.latitude);
            oled_set_cursor(0, 2);
            oled_printf("Lon: %.6f", location.longitude);
            oled_set_cursor(0, 3);
            oled_printf("Test: PASSED");
            return 1;
        }
        
        delay_ms(1000);
        test_count++;
    }
    
    oled_set_cursor(0, 2);
    oled_printf("GPS: Timeout");
    oled_set_cursor(0, 3);
    oled_printf("Test: FAILED");
    
    return 0;
}

/**
 * @brief 缺陷检测测试函数
 * @retval 1-检测功能正常，0-检测功能异常
 */
uint8_t defect_detection_test(void)
{
    defect_info_t defect;
    uint8_t test_count = 0;
    
    oled_clear();
    oled_set_cursor(0, 0);
    oled_printf("Defect Test Start...");
    
    /* 测试缺陷检测功能，等待10秒 */
    while (test_count < 10)
    {
        v831_update();
        defect = v831_get_defect_info();
        
        oled_set_cursor(0, 1);
        oled_printf("Waiting... %ds", test_count);
        oled_set_cursor(0, 2);
        oled_printf("Type:%d Conf:%d%%", defect.type, defect.confidence);
        
        if (defect.detected)
        {
            oled_set_cursor(0, 3);
            if (v831_is_critical_defect(defect.type))
            {
                oled_printf("Critical: YES");
            }
            else
            {
                oled_printf("Critical: NO");
            }
            return 1;
        }
        
        delay_ms(1000);
        test_count++;
    }
    
    oled_set_cursor(0, 3);
    oled_printf("No defect detected");
    
    return 1;  /* 没有检测到缺陷也算正常 */
}

/**
 * @brief 综合系统测试函数
 */
void system_comprehensive_test(void)
{
    uint8_t mqtt_ok = 0;
    uint8_t gps_ok = 0;
    uint8_t defect_ok = 0;
    
    oled_clear();
    oled_set_cursor(0, 0);
    oled_printf("System Test Mode");
    oled_set_cursor(0, 1);
    oled_printf("Press any key...");
    delay_ms(3000);
    
    /* 1. MQTT连接测试 */
    mqtt_ok = mqtt_connection_test();
    delay_ms(3000);
    
    /* 2. GPS定位测试 */
    gps_ok = gps_test();
    delay_ms(3000);
    
    /* 3. 缺陷检测测试 */
    defect_ok = defect_detection_test();
    delay_ms(3000);
    
    /* 显示测试结果 */
    oled_clear();
    oled_set_cursor(0, 0);
    oled_printf("Test Results:");
    oled_set_cursor(0, 1);
    oled_printf("MQTT: %s", mqtt_ok ? "PASS" : "FAIL");
    oled_set_cursor(0, 2);
    oled_printf("GPS:  %s", gps_ok ? "PASS" : "FAIL");
    oled_set_cursor(0, 3);
    oled_printf("CAM:  %s", defect_ok ? "PASS" : "FAIL");
    
    /* 保持显示结果 */
    while (1)
    {
        delay_ms(1000);
    }
}
