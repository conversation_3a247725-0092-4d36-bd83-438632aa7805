#include "oled.h"
#include "../Drivers/SYSTEM/usart/usart.h"
#include "delay.h"
#include <stdio.h>
#include <string.h>
#include <stdarg.h>
#include <stdlib.h>
#include "font.h"    /* 包含字体库 */
#include "pinconfig.h"  /* 包含引脚配置 */

/* I2C相关寄存器位定义 - 避免与STM32库冲突 */
#define OLED_I2C_CR1_PE                 (1U<<0)  /* I2C外设使能 */
#define OLED_I2C_CR2_START              (1U<<13) /* 产生起始条件 */
#define OLED_I2C_CR2_STOP               (1U<<14) /* 产生停止条件 */
#define OLED_I2C_ISR_BUSY               (1U<<15) /* I2C总线忙标志 */
#define OLED_I2C_ISR_TXIS               (1U<<1)  /* 发送寄存器空标志 */
#define OLED_I2C_ISR_TC                 (1U<<6)  /* 传输完成标志 */

/* OLED I2C地址 */
#define OLED_I2C_ADDR              0x78  /* OLED的I2C地址，一般为0x78或0x7A */

/* OLED命令定义 */
#define OLED_CMD_CLEAR             0x01  /* 清屏命令 */
#define OLED_CMD_HOME              0x02  /* 光标归位 */
#define OLED_CMD_SET_CURSOR        0x80  /* 设置光标位置 */

/* 控制字节 */
#define OLED_CONTROL_CMD           0x00  /* 命令控制字节 */
#define OLED_CONTROL_DATA          0x40  /* 数据控制字节 */

/* OLED显存 */
static uint8_t OLED_GRAM[128][8];  /* 显存缓冲区 [128列][8页] */

/* 内部函数声明 */
static void oled_write_cmd(uint8_t cmd);
static void oled_write_data(const uint8_t *data, uint16_t size);
static void i2c_init(void);


static void i2c_init(void)
{
    /* 使能I2C和GPIO时钟 */
    OLED_I2C_CLK_ENABLE();
    OLED_I2C_SDA_GPIO_CLK;
    OLED_I2C_SCL_GPIO_CLK;
    
    /* 配置GPIO */
    sys_gpio_set(OLED_I2C_SCL_GPIO_PORT, 1 << OLED_I2C_SCL_PIN, 
                 SYS_GPIO_MODE_AF, SYS_GPIO_OTYPE_OD,
                 SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);
                 
    sys_gpio_set(OLED_I2C_SDA_GPIO_PORT, 1 << OLED_I2C_SDA_PIN,
                 SYS_GPIO_MODE_AF, SYS_GPIO_OTYPE_OD,
                 SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);
    
    /* 配置引脚复用 
    sys_gpio_af_set(OLED_I2C_SCL_GPIO_PORT, OLED_I2C_SCL_PIN, 6);  
    sys_gpio_af_set(OLED_I2C_SDA_GPIO_PORT, OLED_I2C_SDA_PIN, 5); 
  */
    /* 配置I2C */
    I2C1->TIMINGR = 0x10C0ECFF;  /* 100KHz速度 */
    I2C1->CR1 = OLED_I2C_CR1_PE; /* 使能I2C */
}

void oled_init(void)
{
    /* 初始化I2C */
    i2c_init();
    
    /* OLED显示屏初始化命令序列 */
    uint8_t cmd[] = {
        0xAE, /* 关闭显示 */
        0x20, 0x10, /* 设置寻址模式为页寻址 */
        0xB0, /* 设置页地址为0 */
        0x00, /* 设置列地址低4位为0 */
        0x10, /* 设置列地址高4位为0 */
        0x40, /* 设置显示起始行为0 */
        0x81, 0xCF, /* 设置对比度为0xCF */
        0xA1, /* 段重定向设置 */
        0xA6, /* 正常显示（不反转） */
        0xA8, 0x3F, /* 设置复用率为64 */
        0xC8, /* 行扫描顺序：从上到下 */
        0xD3, 0x00, /* 显示偏移：无 */
        0xD5, 0x80, /* 设置显示时钟分频比/振荡器频率 */
        0xD9, 0xF1, /* 设置预充电周期 */
        0xDA, 0x12, /* 设置COM引脚硬件配置 */
        0xDB, 0x40, /* 设置VCOMH取消选择级别 */
        0x8D, 0x14, /* 使能电荷泵 */
        0xAF  /* 开启显示 */
    };
    
    /* 发送初始化命令序列 */
    for (uint8_t i = 0; i < sizeof(cmd); i++)
    {
        oled_write_cmd(cmd[i]);
    }
    
    /* 清空显示 */
    oled_clear();
}

static void oled_write_cmd(uint8_t cmd)
{
    while(I2C1->ISR & OLED_I2C_ISR_BUSY);

    I2C1->CR2 = ((uint32_t)OLED_I2C_ADDR << 1) | (2 << 16) | OLED_I2C_CR2_START;

    while(!(I2C1->ISR & OLED_I2C_ISR_TXIS));
    I2C1->TXDR = OLED_CONTROL_CMD;

    while(!(I2C1->ISR & OLED_I2C_ISR_TXIS));
    I2C1->TXDR = cmd;

    while(!(I2C1->ISR & OLED_I2C_ISR_TC));
    I2C1->CR2 |= OLED_I2C_CR2_STOP;
}

static void oled_write_data(const uint8_t *data, uint16_t size)
{
    while(I2C1->ISR & OLED_I2C_ISR_BUSY);  /* 等待I2C空闲 */

    /* 配置传输 */
    I2C1->CR2 = ((uint32_t)OLED_I2C_ADDR << 1) |  /* 从机地址 */
                ((size + 1) << 16) |               /* 发送size+1字节 */
                OLED_I2C_CR2_START;                /* 产生起始条件 */

    while(!(I2C1->ISR & OLED_I2C_ISR_TXIS));  /* 等待发送寄存器空 */
    I2C1->TXDR = OLED_CONTROL_DATA;            /* 发送控制字节 */

    for(uint16_t i = 0; i < size; i++)
    {
        while(!(I2C1->ISR & OLED_I2C_ISR_TXIS));  /* 等待发送寄存器空 */
        I2C1->TXDR = data[i];                      /* 发送数据字节 */
    }

    while(!(I2C1->ISR & OLED_I2C_ISR_TC));    /* 等待传输完成 */
    I2C1->CR2 |= OLED_I2C_CR2_STOP;           /* 产生停止条件 */
}


void oled_clear(void)
{
    uint8_t i;
    uint8_t data[128];
    
    /* 填充数据缓冲区为0 */
    memset(data, 0, sizeof(data));
    
    /* 逐页清空 */
    for (i = 0; i < 8; i++)
    {
        oled_write_cmd(0xB0 + i);  /* 设置页地址 */
        oled_write_cmd(0x00);      /* 设置列地址低4位 */
        oled_write_cmd(0x10);      /* 设置列地址高4位 */
        
        /* 写入一页数据 */
        oled_write_data(data, sizeof(data));
    }
}


void oled_set_cursor(uint8_t x, uint8_t y)
{
    oled_write_cmd(0xB0 + y);                /* 设置页地址 */
    oled_write_cmd(0x00 + (x & 0x0F));      /* 设置列地址低4位 */
    oled_write_cmd(0x10 + ((x >> 4) & 0x0F));/* 设置列地址高4位 */
}


void oled_printf(const char *format, ...)
{
    char buffer[128];
    va_list args;
    uint8_t i = 0;
    uint8_t x, y;
    
    /* 获取当前光标位置 */
    /* 注意：这里需要维护全局变量来跟踪光标位置 */
    /* 或者从OLED控制器读取当前位置（如果支持） */
    /* 简化实现：使用静态变量记录上次设置的位置 */
    static uint8_t current_x = 0;
    static uint8_t current_y = 0;
    
    x = current_x;
    y = current_y;
    
    va_start(args, format);
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    
    while (buffer[i] != '\0' && i < sizeof(buffer))
    {
        /* 处理换行符 */
        if (buffer[i] == '\n')
        {
            x = 0;
            y++;
            if (y >= OLED_ROWS)
            {
                y = 0;  /* 超出屏幕范围，回到顶部 */
            }
            i++;
            continue;
        }
        
     
        if (buffer[i] == '\r')
        {
            x = 0;
            i++;
            continue;
        }
        
        /* 显示字符 */

        oled_display_char(x * 6, y, buffer[i], 1);  /* 使用6x8字体 */
        
        /* 更新光标位置 */
        x++;
        if (x >= OLED_COLS)
        {
            x = 0;
            y++;
            if (y >= OLED_ROWS)
            {
                y = 0;  /* 超出屏幕范围，回到顶部 */
            }
        }
        
        i++;
    }
    
    /* 更新当前光标位置 */
    current_x = x;
    current_y = y;
}


void oled_display_image(const uint8_t *image, uint16_t size)
{
    if (size > 1024) size = 1024;  /* 限制大小，防止溢出 */
    oled_write_data(image, size);
}


void oled_display_char(uint8_t x, uint8_t y, uint8_t chr, uint8_t size)
{
    uint8_t temp;
    uint8_t pos, t;
    
    /* 设置显示位置 */
    oled_set_cursor(x, y);
    
    /* 获取字符的点阵数据 */
    const uint8_t *font_data = get_font_data(chr);
    if (font_data != NULL)
    {
        /* 发送字符的点阵数据 */
        for (pos = 0; pos < size; pos++)
        {
            temp = font_data[pos];
            for (t = 0; t < 8; t++)
            {
                if (temp & 0x80)
                {
                    oled_draw_point(x + t, y + pos, 1);
                }
                else
                {
                    oled_draw_point(x + t, y + pos, 0);
                }
                temp <<= 1;
            }
        }
    }
}


void oled_draw_point(uint8_t x, uint8_t y, uint8_t dot)
{
    uint8_t page;    /* 页地址 */
    uint8_t bit;     /* 位地址 */
    
    if (x > 127 || y > 63) return;  /* 超出范围 */
    
    page = y / 8;    /* 计算页地址 (0~7) */
    bit = y % 8;     /* 计算位地址 (0~7) */
    
    if (dot)
    {
        OLED_GRAM[x][page] |= 1 << bit;   /* 点亮 */
    }
    else
    {
        OLED_GRAM[x][page] &= ~(1 << bit); /* 熄灭 */
    }
    
    /* 更新显示 */
    oled_set_cursor(x, page);
    oled_write_data(&OLED_GRAM[x][page], 1);
}


void oled_refresh_gram(void)
{
    uint8_t i, n;
    
    for (i = 0; i < 8; i++)  /* 8页 */
    {
        oled_set_cursor(0, i);  /* 设置起始位置 */
        for (n = 0; n < 128; n++)
        {
            oled_write_data(&OLED_GRAM[n][i], 1);
        }
    }
}
