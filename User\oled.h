#ifndef __OLED_H
#define __OLED_H

#include "sys.h"
#include "delay.h"
#include "../Drivers/SYSTEM/usart/usart.h"
#include "../Drivers/CMSIS/Device/ST/STM32H7xx/Include/stm32h7xx.h"

#include "pinconfig.h"  /* 引入引脚配置 */

/* OLED I2C设备地址 */
#define OLED_I2C_ADDR           0x78  /* OLED的I2C设备地址 */

/* OLED屏幕尺寸定义 */
#define OLED_WIDTH              128   /* OLED宽度 */
#define OLED_HEIGHT             64    /* OLED高度 */
#define OLED_ROWS               8     /* OLED行数（每行8像素） */
#define OLED_COLS              21    /* 每行字符数(按6x8字体计算) */

/* 函数声明 */
void oled_init(void);                    /* 初始化OLED显示屏 */
void oled_clear(void);                   /* 清空显示 */
void oled_set_cursor(uint8_t x, uint8_t y); /* 设置光标位置 */
void oled_printf(const char *format, ...);   /* 显示格式化字符串 */
void oled_display_image(const uint8_t *image, uint16_t size); /* 显示图像 */
void oled_display_on(void);
void oled_display_off(void);
void oled_refresh_gram(void);
void oled_draw_point(uint8_t x, uint8_t y, uint8_t dot);
void oled_display_char(uint8_t x, uint8_t y, uint8_t chr, uint8_t size);
void oled_display_string(uint8_t x, uint8_t y, const char *str, uint8_t size);
void oled_display_chinese(uint8_t x, uint8_t y, uint8_t no);
void oled_test(void);                    /* OLED测试函数 */

#endif /* __OLED_H */
