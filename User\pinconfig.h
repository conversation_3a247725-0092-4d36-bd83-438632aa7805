#ifndef __PINCONFIG_H
#define __PINCONFIG_H

#include "stm32h7xx.h"

/*************************************************
 * OLED I2C屏幕 - 使用I2C1
 *************************************************/
#define OLED_I2C                   I2C1
#define OLED_I2C_CLK_ENABLE()      (RCC->APB1LENR |= (1 << 21))
#define OLED_I2C_SDA_GPIO_PORT     GPIOB
#define OLED_I2C_SDA_GPIO_CLK      (RCC->AHB4ENR |= (1 << 1))
#define OLED_I2C_SDA_PIN           5   /* 根据接线图修正: PB5 */
#define OLED_I2C_SCL_GPIO_PORT     GPIOB
#define OLED_I2C_SCL_GPIO_CLK      (RCC->AHB4ENR |= (1 << 1))
#define OLED_I2C_SCL_PIN           6   /* PB6 */

/*************************************************
 * 激光测距模块 - 使用I2C1（与OLED共用I2C总线）
 *************************************************/
#define VL53L0X_I2C                I2C1
#define VL53L0X_I2C_ADDR           0x29
/* VL53L0X使用与OLED相同的I2C引脚，但需要独立的XSHUT控制引脚 */
#define VL53L0X_XSHUT_GPIO_PORT    GPIOB
#define VL53L0X_XSHUT_GPIO_CLK     (RCC->AHB4ENR |= (1 << 1))  /* GPIOB时钟使能 */
#define VL53L0X_XSHUT_PIN          7   /* PB7作为XSHUT控制引脚 */

/*************************************************
 * 电机驱动模块 - 使用PWM1/PWM2
 *************************************************/
#define MOTOR_PWM1_GPIO_PORT       GPIOC
#define MOTOR_PWM1_GPIO_CLK        (RCC->AHB4ENR |= (1 << 2))
#define MOTOR_PWM1_PIN             7   /* 根据U4连接器的PWM1引脚确定 */

#define MOTOR_PWM2_GPIO_PORT       GPIOC
#define MOTOR_PWM2_GPIO_CLK        (RCC->AHB4ENR |= (1 << 2))
#define MOTOR_PWM2_PIN             8   /* 根据U4连接器的PWM2引脚确定 */

/*************************************************
 * ESP8266 WiFi模块 - 使用USART3 (避免与V831冲突)
 *************************************************/
#define ESP8266_USART              USART3
#define ESP8266_USART_CLK_ENABLE() (RCC->APB1LENR |= (1 << 18))
#define ESP8266_TX_GPIO_PORT       GPIOD
#define ESP8266_TX_GPIO_CLK        (RCC->AHB4ENR |= (1 << 3))
#define ESP8266_TX_PIN             8   /* PD8 (USART3_TX) */
#define ESP8266_RX_GPIO_PORT       GPIOD
#define ESP8266_RX_GPIO_CLK        (RCC->AHB4ENR |= (1 << 3))
#define ESP8266_RX_PIN             9   /* PD9 (USART3_RX) */

/*************************************************
 * GTU7-GPS模块 - 使用USART2
 *************************************************/
#define GPS_USART                  USART2
#define GPS_USART_CLK_ENABLE()     (RCC->APB1LENR |= (1 << 17))
#define GPS_TX_GPIO_PORT           GPIOD
#define GPS_TX_GPIO_CLK            (RCC->AHB4ENR |= (1 << 3))  /* GPIOD时钟使能 */
#define GPS_TX_PIN                 5   /* 根据接线图修正: PD5 (USART2_TX) */
#define GPS_RX_GPIO_PORT           GPIOD
#define GPS_RX_GPIO_CLK            (RCC->AHB4ENR |= (1 << 3))  /* GPIOD时钟使能 */
#define GPS_RX_PIN                 6   /* 根据接线图修正: PD6 (USART2_RX) */
#define GPS_PD_GPIO_PORT           GPIOD
#define GPS_PD_GPIO_CLK            (RCC->AHB4ENR |= (1 << 3))
#define GPS_PD_PIN                 11  /* 根据接线图添加: PD11控制引脚 */

/*************************************************
 * V831摄像头模块 - 使用TYPE C UART (USART1)
 * 注意: 与ESP8266共用USART1，不可同时使用
 *************************************************/
#define V831_USART                 USART1
#define V831_USART_CLK_ENABLE()    (RCC->APB2ENR |= (1 << 4))  /* USART1时钟使能 */
#define V831_TX_GPIO_PORT          GPIOA
#define V831_TX_GPIO_CLK           (RCC->AHB4ENR |= (1 << 0))  /* GPIOA时钟使能 */
#define V831_TX_PIN                9   /* PA9 (USART1_TX) */
#define V831_RX_GPIO_PORT          GPIOA
#define V831_RX_GPIO_CLK           (RCC->AHB4ENR |= (1 << 0))  /* GPIOA时钟使能 */
#define V831_RX_PIN                10  /* PA10 (USART1_RX) */

/*************************************************
 * 舵机模块 - 使用PWM3
 *************************************************/
#define SERVO_PWM_GPIO_PORT        GPIOB
#define SERVO_PWM_GPIO_CLK         (RCC->AHB4ENR |= (1 << 1))
#define SERVO_PWM_PIN              3   /* 根据H4连接器的PWM3引脚确定 */

#endif /* __PINCONFIG_H */ 
