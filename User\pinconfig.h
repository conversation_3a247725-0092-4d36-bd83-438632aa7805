#ifndef __PINCONFIG_H
#define __PINCONFIG_H

#include "stm32h7xx.h"

/*************************************************
 * OLED I2C屏幕 - 使用I2C1
 * 根据用户要求：SCL接P1 39引脚(PD2)，SDA接P1 38引脚(PD3)
 *************************************************/
#define OLED_I2C                   I2C1
#define OLED_I2C_CLK_ENABLE()      (RCC->APB1LENR |= (1 << 21))
#define OLED_I2C_SDA_GPIO_PORT     GPIOD
#define OLED_I2C_SDA_GPIO_CLK      (RCC->AHB4ENR |= (1 << 3))  /* GPIOD时钟使能 */
#define OLED_I2C_SDA_PIN           3   /* P1 38引脚: PD3 (I2C1_SDA) */
#define OLED_I2C_SCL_GPIO_PORT     GPIOD
#define OLED_I2C_SCL_GPIO_CLK      (RCC->AHB4ENR |= (1 << 3))  /* GPIOD时钟使能 */
#define OLED_I2C_SCL_PIN           2   /* P1 39引脚: PD2 (I2C1_SCL) */



/*************************************************
 * 电机驱动模块 - 根据背面引脚图配置
 *************************************************/
#define MOTOR_PWM1_GPIO_PORT       GPIOA
#define MOTOR_PWM1_GPIO_CLK        (RCC->AHB4ENR |= (1 << 0))  /* GPIOA时钟使能 */
#define MOTOR_PWM1_PIN             8   /* P2 45引脚: PA8 */

#define MOTOR_PWM2_GPIO_PORT       GPIOC
#define MOTOR_PWM2_GPIO_CLK        (RCC->AHB4ENR |= (1 << 2))  /* GPIOC时钟使能 */
#define MOTOR_PWM2_PIN             9   /* P2 46引脚: PC9 */

/*************************************************
 * ESP8266 WiFi模块 - 根据背面引脚图配置USART3
 *************************************************/
#define ESP8266_USART              USART3
#define ESP8266_USART_CLK_ENABLE() (RCC->APB1LENR |= (1 << 18))
#define ESP8266_TX_GPIO_PORT       GPIOD
#define ESP8266_TX_GPIO_CLK        (RCC->AHB4ENR |= (1 << 3))  /* GPIOD时钟使能 */
#define ESP8266_TX_PIN             8   /* P1 8引脚: PD8 (USART3_TX) */
#define ESP8266_RX_GPIO_PORT       GPIOD
#define ESP8266_RX_GPIO_CLK        (RCC->AHB4ENR |= (1 << 3))  /* GPIOD时钟使能 */
#define ESP8266_RX_PIN             9   /* P1 10引脚: PD9 (USART3_RX) */

/*************************************************
 * GTU7-GPS模块 - 根据背面引脚图配置USART2
 *************************************************/
#define GPS_USART                  USART2
#define GPS_USART_CLK_ENABLE()     (RCC->APB1LENR |= (1 << 17))
#define GPS_TX_GPIO_PORT           GPIOD
#define GPS_TX_GPIO_CLK            (RCC->AHB4ENR |= (1 << 3))  /* GPIOD时钟使能 */
#define GPS_TX_PIN                 5   /* P1 5引脚: PD5 (USART2_TX) */
#define GPS_RX_GPIO_PORT           GPIOD
#define GPS_RX_GPIO_CLK            (RCC->AHB4ENR |= (1 << 3))  /* GPIOD时钟使能 */
#define GPS_RX_PIN                 6   /* P1 7引脚: PD6 (USART2_RX) */
#define GPS_PD_GPIO_PORT           GPIOD
#define GPS_PD_GPIO_CLK            (RCC->AHB4ENR |= (1 << 3))  /* GPIOD时钟使能 */
#define GPS_PD_PIN                 11  /* P1 13引脚: PD11 GPS控制引脚 */

/*************************************************
 * V831摄像头模块 - 根据背面引脚图配置USART1
 *************************************************/
#define V831_USART                 USART1
#define V831_USART_CLK_ENABLE()    (RCC->APB2ENR |= (1 << 4))  /* USART1时钟使能 */
#define V831_TX_GPIO_PORT          GPIOA
#define V831_TX_GPIO_CLK           (RCC->AHB4ENR |= (1 << 0))  /* GPIOA时钟使能 */
#define V831_TX_PIN                9   /* P2 21引脚: PA9 (USART1_TX) */
#define V831_RX_GPIO_PORT          GPIOA
#define V831_RX_GPIO_CLK           (RCC->AHB4ENR |= (1 << 0))  /* GPIOA时钟使能 */
#define V831_RX_PIN                10  /* P2 23引脚: PA10 (USART1_RX) */

/*************************************************
 * 舵机模块 - 根据背面引脚图配置
 *************************************************/
#define SERVO_PWM_GPIO_PORT        GPIOB
#define SERVO_PWM_GPIO_CLK         (RCC->AHB4ENR |= (1 << 1))  /* GPIOB时钟使能 */
#define SERVO_PWM_PIN              0   /* P2 26引脚: PB0 舵机PWM控制引脚 */

#endif /* __PINCONFIG_H */ 
