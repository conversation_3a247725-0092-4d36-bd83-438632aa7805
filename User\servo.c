#include "sys.h"
#include "delay.h"
#include <stdio.h>
#include "servo.h"
#include "pinconfig.h"  /* 引入引脚配置 */

/* 舵机当前状态 */
static servo_state_t g_servo_state = SERVO_STATE_OPEN;

/**
 * @brief       舵机初始化
 * @param       无
 * @retval      无
 * @note        初始化舵机控制引脚和默认状态
 */
void servo_init(void)
{
    /* 使能GPIO时钟 */
    SERVO_PWM_GPIO_CLK;  /* 使用pinconfig.h中定义的宏 */

    /* 配置舵机PWM引脚 */
    sys_gpio_set(SERVO_PWM_GPIO_PORT, 1 << SERVO_PWM_PIN,
                 SYS_GPIO_MODE_OUT, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);

    /* 舵机初始位置设为0度 */
    g_servo_state = SERVO_STATE_OPEN;

    /* 使舵机运动到初始位置(0度) */
    servo_set_position(0);
}

void servo_generate_pwm(uint16_t pulse_width_us, uint8_t num_cycles)
{
    uint8_t i;
    
    /* 生成指定数量的PWM周期 */
    for (i = 0; i < num_cycles; i++)
    {
        /* 高电平 - 脉冲宽度决定舵机位置 */
        sys_gpio_pin_set(SERVO_PWM_GPIO_PORT, 1 << SERVO_PWM_PIN, 1);
        delay_us(pulse_width_us);
        
        /* 低电平 - 补足20ms周期 */
        sys_gpio_pin_set(SERVO_PWM_GPIO_PORT, 1 << SERVO_PWM_PIN, 0);
        delay_us(20000 - pulse_width_us);  /* 20ms周期 */
    }
}


void servo_set_position(uint16_t pos)
{
    uint16_t pulse_width;

    /* 限制位置范围 */
    if (pos > SERVO_POS_MAX) pos = SERVO_POS_MAX;

    /* 将位置值转换为脉冲宽度 (0.5ms-2.5ms for 0-270度) */
    /* 计算公式: 0.5ms + (角度/270) * 2ms */
    pulse_width = 500 + (uint16_t)(pos * 7.41);  /* 0度=0.5ms(500us), 270度=2.5ms(2500us) */

    /* 生成15个PWM周期，确保舵机稳定运动到指定位置 */
    servo_generate_pwm(pulse_width, 15);

    /* 更新舵机状态 */
    if (pos >= 135)  /* 超过135度认为是关闭状态 */
    {
        g_servo_state = SERVO_STATE_CLOSE;
    }
    else if (pos <= 45)  /* 小于45度认为是打开状态 */
    {
        g_servo_state = SERVO_STATE_OPEN;
    }
    else
    {
        g_servo_state = SERVO_STATE_MOVING;
    }
}


/**
 * @brief       舵机自动旋转270度
 * @param       无
 * @retval      无
 * @note        从0度开始，逐步旋转到270度，每次旋转5度，每步延时50ms
 */
void servo_auto_rotate_270(void)
{
    uint16_t pos;

    g_servo_state = SERVO_STATE_MOVING;

    /* 从0度开始旋转到270度 */
    for (pos = 0; pos <= SERVO_POS_MAX; pos += SERVO_ROTATION_STEP)
    {
        servo_set_position(pos);
        delay_ms(SERVO_ROTATION_DELAY);  /* 每步之间延时 */
    }

    /* 确保最后到达270度 */
    servo_set_position(SERVO_POS_MAX);
    g_servo_state = SERVO_STATE_CLOSE;  /* 270度时设为关闭状态 */
}


servo_state_t servo_get_state(void)
{
    return g_servo_state;
} 

