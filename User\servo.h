#ifndef __SERVO_H
#define __SERVO_H

#include "sys.h"
#include "pinconfig.h"  /* 引入引脚配置 */

/* 舵机状态枚举定义 */
typedef enum {
    SERVO_STATE_OPEN = 0,    /* 舵机打开状态 */
    SERVO_STATE_CLOSE,       /* 舵机闭合状态 */
    SERVO_STATE_MOVING       /* 舵机运动中状态 */
} servo_state_t;

/* 舵机位置定义 */
#define SERVO_POS_MIN       0    /* 最小位置 */
#define SERVO_POS_MAX       270  /* 最大位置 - 支持270度旋转 */
#define SERVO_ROTATION_STEP 5    /* 每次旋转步长 */
#define SERVO_ROTATION_DELAY 50  /* 每步之间的延时(ms) */

/* 函数声明 */
void servo_init(void);                  /* 舵机初始化 */
void servo_set_position(uint16_t pos);  /* 设置舵机位置(支持0-270度) */
void servo_auto_rotate_270(void);       /* 自动旋转270度 */
servo_state_t servo_get_state(void);    /* 获取舵机状态 */

#endif /* __SERVO_H */ 

