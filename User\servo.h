#ifndef __SERVO_H
#define __SERVO_H

#include "sys.h"
#include "pinconfig.h"  /* 引入引脚配置 */

/* 舵机状态枚举定义 */
typedef enum {
    SERVO_STATE_OPEN = 0,    /* 舵机打开状态 */
    SERVO_STATE_CLOSE,       /* 舵机闭合状态 */
    SERVO_STATE_MOVING       /* 舵机运动中状态 */
} servo_state_t;

/* 舵机位置定义 */
#define SERVO_POS_MIN       0    /* 最小位置 */
#define SERVO_POS_MAX       180  /* 最大位置 */
#define SERVO_POS_OPEN      30   /* 打开位置 */
#define SERVO_POS_CLOSE     150  /* 闭合位置 */

#define SERVO_DEFECT_THRESHOLD  75  /* 缺陷检测阈值 */

/* 函数声明 */
void servo_init(void);                  /* 舵机初始化 */
void servo_set_position(uint8_t pos);   /* 设置舵机位置 */
void servo_clamp_open(void);            /* 打开夹具 */
void servo_clamp_close(void);           /* 闭合夹具 */
servo_state_t servo_get_state(void);    /* 获取舵机状态 */

#endif /* __SERVO_H */ 

