#ifndef __STDLIB_EXT_H
#define __STDLIB_EXT_H

#ifdef __cplusplus
extern "C" {
#endif

/* 标准库常量和函数定义，在系统头文件不可用时使用 */
#ifndef _STDLIB_H

/* 退出状态定义 */
#define EXIT_FAILURE    1  /* 失败退出状态 */
#define EXIT_SUCCESS    0  /* 成功退出状态 */

/* 常量定义 */
#ifndef NULL
#define NULL ((void *)0)  /* 空指针定义 */
#endif

/* 类型定义 */
typedef unsigned int size_t;
typedef int wchar_t;

/* 内存管理函数声明 */
void *malloc(size_t size);
void free(void *ptr);
void *calloc(size_t nmemb, size_t size);
void *realloc(void *ptr, size_t size);

/* 数值转换函数声明 */
int atoi(const char *nptr);
long atol(const char *nptr);
double atof(const char *nptr);

/* 随机数函数声明 */
int rand(void);
void srand(unsigned int seed);

/* 排序和搜索函数声明 */
void qsort(void *base, size_t nmemb, size_t size, int (*compar)(const void *, const void *));
void *bsearch(const void *key, const void *base, size_t nmemb, size_t size, int (*compar)(const void *, const void *));

#endif /* _STDLIB_H */

#ifdef __cplusplus
}
#endif

#endif /* __STDLIB_EXT_H */ 

