#include "../Drivers/CMSIS/Device/ST/STM32H7xx/Include/stm32h7xx.h"  /* 包含STM32H7的寄存器定义 */
#include "sys.h"        /* 包含系统函数声明 */

/**
 * @brief       设置GPIO引脚复用功能
 * @param       gpiox: GPIO端口, 如GPIOA, GPIOB等
 * @param       pinx:  GPIO引脚, 如0, 1等
 * @param       afx:   复用功能选择, 范围0~15
 * @retval      无
 */
void sys_gpio_af_set(GPIO_TypeDef *gpiox, uint16_t pinx, uint8_t afx)
{
    uint32_t pinpos = 0, pinbit = 0, pin = pinx;
    
    /* 计算引脚位置 */
    for (pinpos = 0; pinpos < 16; pinpos++)
    {
        pinbit = 1 << pinpos;
        if ((pin & pinbit) == pinbit)
        {
            /* 设置复用功能寄存器 */
            if (pinpos < 8)
            {
                /* 低8位引脚 */
                gpiox->AFR[0] &= ~(0xF << (pinpos * 4));
                gpiox->AFR[0] |= (afx << (pinpos * 4));
            }
            else
            {
                /* 高8位引脚 */
                gpiox->AFR[1] &= ~(0xF << ((pinpos - 8) * 4));
                gpiox->AFR[1] |= (afx << ((pinpos - 8) * 4));
            }
        }
    }
} 

/**
 * @brief       延时毫秒函数
 * @param       ms: 要延时的毫秒数
 * @retval      无
 */
void sys_delay_ms(uint32_t ms)
{
    uint32_t i;
    for (i = 0; i < ms * 10000; i++)
    {
        __NOP();  /* 空操作，延时 */
    }
}

/**
 * @brief       延时微秒函数
 * @param       us: 要延时的微秒数
 * @retval      无
 */
void sys_delay_us(uint32_t us)
{
    uint32_t i;
    for (i = 0; i < us * 10; i++)
    {
        __NOP();  /* 空操作，延时 */
    }
}

