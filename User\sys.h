#ifndef _SYS_H
#define _SYS_H

#include "../Drivers/CMSIS/Device/ST/STM32H7xx/Include/stm32h7xx.h"

/* GPIO引脚配置参数定义 */
/* GPIO模式定义 */
#define SYS_GPIO_MODE_IN       0   /* 输入 */
#define SYS_GPIO_MODE_OUT      1   /* 输出 */
#define SYS_GPIO_MODE_AF       2   /* 复用功能 */
#define SYS_GPIO_MODE_AIN      3   /* 模拟输入 */

/* GPIO输出类型定义 */
#define SYS_GPIO_OTYPE_PP      0   /* 推挽输出 */
#define SYS_GPIO_OTYPE_OD      1   /* 开漏输出 */

/* GPIO输出速度定义 */
#define SYS_GPIO_SPEED_LOW     0   /* 低速 */
#define SYS_GPIO_SPEED_MID     1   /* 中速 */
#define SYS_GPIO_SPEED_FAST    2   /* 高速 */
#define SYS_GPIO_SPEED_HIGH    3   /* 超高速 */

/* GPIO上下拉定义 */
#define SYS_GPIO_PUPD_NONE     0   /* 无上下拉 */
#define SYS_GPIO_PUPD_PU       1   /* 上拉 */
#define SYS_GPIO_PUPD_PD       2   /* 下拉 */

/* GPIO端口定义 */
#define SYS_GPIO_PIN0          1<<0
#define SYS_GPIO_PIN1          1<<1
#define SYS_GPIO_PIN2          1<<2
#define SYS_GPIO_PIN3          1<<3
#define SYS_GPIO_PIN4          1<<4
#define SYS_GPIO_PIN5          1<<5
#define SYS_GPIO_PIN6          1<<6
#define SYS_GPIO_PIN7          1<<7
#define SYS_GPIO_PIN8          1<<8
#define SYS_GPIO_PIN9          1<<9
#define SYS_GPIO_PIN10         1<<10
#define SYS_GPIO_PIN11         1<<11
#define SYS_GPIO_PIN12         1<<12
#define SYS_GPIO_PIN13         1<<13
#define SYS_GPIO_PIN14         1<<14
#define SYS_GPIO_PIN15         1<<15

/* 函数声明 */
void sys_stm32_clock_init(uint32_t plln, uint32_t pllm, uint32_t pllp, uint32_t pllq);
void sys_gpio_set(GPIO_TypeDef *p_gpiox, uint16_t pinx, uint32_t mode, uint32_t otype, uint32_t speed, uint32_t pupd);
void sys_gpio_pin_set(GPIO_TypeDef *p_gpiox, uint16_t pinx, uint8_t status);
uint8_t sys_gpio_pin_get(GPIO_TypeDef *p_gpiox, uint16_t pinx);
void sys_gpio_af_set(GPIO_TypeDef *gpiox, uint16_t pinx, uint8_t afx);
void sys_delay_ms(uint32_t ms);
void sys_delay_us(uint32_t us);

#endif 

