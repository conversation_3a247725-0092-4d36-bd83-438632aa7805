#include "sys.h"
#include "delay.h"
#include "../Drivers/SYSTEM/usart/usart.h"
#include "v831_camera.h"
#include <string.h>
#include <stdlib.h>
#include <stdio.h>

extern uint32_t HAL_GetTick(void);

#define V831_RX_BUFFER_SIZE 512
static uint8_t v831_rx_buffer[V831_RX_BUFFER_SIZE];
static uint16_t v831_rx_count = 0;
static uint8_t v831_buffer_overflow = 0;

/* 当前缺陷信息 */
static defect_info_t current_defect = {0, 0, 0, 0};

/* 通信错误计数 */
static uint8_t v831_error_count = 0;
#define V831_MAX_ERRORS 5

/**
 * @brief V831接收回调函数
 * @param data: 接收到的数据
 * @retval 无
 */
void v831_rx_callback(uint8_t data)
{
    /* 检查缓冲区是否有足够空间 */
    if (v831_rx_count < V831_RX_BUFFER_SIZE - 1)
    {
        v831_rx_buffer[v831_rx_count++] = data;
        
        /* 如果收到帧结束标记，添加字符串结束符 */
        if (data == '\n' || data == '\r')
        {
            v831_rx_buffer[v831_rx_count] = '\0';
        }
    }
    else
    {
        /* 缓冲区溢出，设置溢出标志 */
        v831_buffer_overflow = 1;
    }
}

static void v831_uart_init(void)
{
    V831_TX_GPIO_CLK;
    V831_RX_GPIO_CLK;
    V831_USART_CLK_ENABLE();

    sys_gpio_set(V831_TX_GPIO_PORT, 1 << V831_TX_PIN,
                 SYS_GPIO_MODE_AF, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);

    sys_gpio_set(V831_RX_GPIO_PORT, 1 << V831_RX_PIN,
                 SYS_GPIO_MODE_AF, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);

    sys_gpio_af_set(V831_TX_GPIO_PORT, V831_TX_PIN, 7);
    sys_gpio_af_set(V831_RX_GPIO_PORT, V831_RX_PIN, 7);
    
    /* 配置USART */
    V831_USART->CR1 = 0;        /* 清零CR1寄存器 */
    V831_USART->CR1 |= 1 << 0;  /* 使能接收 */
    V831_USART->CR1 |= 1 << 2;  /* 使能发送 */
    V831_USART->CR1 |= 1 << 5;  /* 接收中断使能 */
    
    /* 配置波特率 - 基于130MHz APB2时钟 */
    uint32_t temp = (130 * 1000000 + 115200 / 2) / 115200;
    V831_USART->BRR = temp;
    V831_USART->CR1 |= 1 << 0;
    NVIC_SetPriority(USART1_IRQn, 3);  /* 设置中断优先级 */
    NVIC_EnableIRQ(USART1_IRQn);       /* 使能USART1中断 */
    usart_register_rx_callback(v831_rx_callback);
}

/**
 * @brief 发送命令到V831
 * @param cmd: 命令字符串
 * @retval 无
 */
static void v831_send_command(const char *cmd)
{
    uint16_t i = 0;
    
    /* 发送命令字符串 */
    while (cmd[i] != '\0')
    {
        V831_USART->TDR = cmd[i++];
        while (!(V831_USART->ISR & (1 << 6)));  /* 等待发送完成 */
    }
    
    /* 发送换行符 */
    V831_USART->TDR = '\r';
    while (!(V831_USART->ISR & (1 << 6)));
    V831_USART->TDR = '\n';
    while (!(V831_USART->ISR & (1 << 6)));
}

/**
 * @brief 等待V831响应
 * @param ack: 期望的响应字符串
 * @param timeout_ms: 超时时间(毫秒)
 * @retval 1: 成功, 0: 失败
 */
static uint8_t v831_wait_response(const char *ack, uint16_t timeout_ms)
{
    uint32_t start_time = HAL_GetTick();
    
    while (HAL_GetTick() - start_time < timeout_ms)
    {
        if (strstr((char *)v831_rx_buffer, ack) != NULL)
        {
            return 1;  /* 找到期望的响应 */
        }
        delay_ms(1);
    }
    
    return 0;  /* 超时 */
}

/**
 * @brief 初始化V831摄像头模块
 * @param 无
 * @retval 无
 */
void v831_init(void)
{
    /* 初始化UART通信 */
    v831_uart_init();
    
    /* 清空接收缓冲区 */
    memset(v831_rx_buffer, 0, V831_RX_BUFFER_SIZE);
    v831_rx_count = 0;
    v831_buffer_overflow = 0;
    v831_error_count = 0;
    
    /* 等待V831启动完成 */
    delay_ms(2000);
    
    /* 发送初始化命令 */
    v831_send_command("INIT");
    
    /* 等待初始化响应 */
    if (!v831_wait_response("OK", 1000))
    {
        /* 初始化失败，重试一次 */
        delay_ms(500);
        v831_send_command("INIT");
        if (!v831_wait_response("OK", 1000))
        {
            /* 再次失败，增加错误计数 */
            v831_error_count++;
        }
    }
    
    /* 发送配置命令，设置检测阈值 */
    char cmd[32];
    snprintf(cmd, sizeof(cmd), "THRESHOLD %d", DEFECT_THRESHOLD);
    v831_send_command(cmd);
    
    /* 等待配置响应 */
    v831_wait_response("OK", 500);
    
    /* 发送开始检测命令 */
    v831_send_command("START");
    
    /* 初始化缺陷信息 */
    current_defect.detected = 0;
    current_defect.type = DEFECT_TYPE_NONE;
    current_defect.confidence = 0;
    current_defect.position = 0;
}

/**
 * @brief 解析V831返回的数据
 * @param 无
 * @retval 无
 */
static void v831_parse_data(void)
{
    char *token;
    char *save_ptr;
    char buffer_copy[V831_RX_BUFFER_SIZE];
    
    /* 检查是否有有效数据 */
    if (v831_rx_count < 3)
    {
        return;
    }
    
    /* 复制缓冲区，避免解析过程中数据被覆盖 */
    strncpy(buffer_copy, (char *)v831_rx_buffer, V831_RX_BUFFER_SIZE - 1);
    buffer_copy[V831_RX_BUFFER_SIZE - 1] = '\0';
    
    /* 查找缺陷检测数据标记 */
    if (strstr(buffer_copy, "DEFECT:") != NULL)
    {
        /* 分割字符串，提取缺陷信息 */
        token = strtok_r(buffer_copy, ":", &save_ptr);
        if (token != NULL && strcmp(token, "DEFECT") == 0)
        {
            /* 提取检测标志 */
            token = strtok_r(NULL, ",", &save_ptr);
            if (token != NULL)
            {
                current_defect.detected = (token[0] == '1');
                
                if (current_defect.detected)
                {
                    /* 提取缺陷类型 */
                    token = strtok_r(NULL, ",", &save_ptr);
                    if (token != NULL)
                    {
                        current_defect.type = atoi(token);
                    }
                    
                    /* 提取置信度 */
                    token = strtok_r(NULL, ",", &save_ptr);
                    if (token != NULL)
                    {
                        current_defect.confidence = atoi(token);
                    }
                    
                    /* 提取位置 */
                    token = strtok_r(NULL, ",", &save_ptr);
                    if (token != NULL)
                    {
                        current_defect.position = atoi(token);
                    }
                }
                else
                {
                    /* 未检测到缺陷，重置信息 */
                    current_defect.type = DEFECT_TYPE_NONE;
                    current_defect.confidence = 0;
                    current_defect.position = 0;
                }
            }
        }
    }
    else if (strstr(buffer_copy, "ERROR:") != NULL)
    {
        /* 处理错误响应 */
        v831_error_count++;
        
        /* 如果错误过多，尝试重新初始化 */
        if (v831_error_count >= V831_MAX_ERRORS)
        {
            v831_init();
        }
    }
}

/**
 * @brief 更新V831状态，处理接收到的数据
 * @param 无
 * @retval 无
 */
void v831_update(void)
{
    /* 检查是否有数据需要处理 */
    if (v831_rx_count > 0)
    {
        /* 解析接收到的数据 */
        v831_parse_data();
        
        /* 清空接收缓冲区 */
        memset(v831_rx_buffer, 0, V831_RX_BUFFER_SIZE);
        v831_rx_count = 0;
    }
    
    /* 检查缓冲区溢出 */
    if (v831_buffer_overflow)
    {
        /* 清空缓冲区并重置溢出标志 */
        memset(v831_rx_buffer, 0, V831_RX_BUFFER_SIZE);
        v831_rx_count = 0;
        v831_buffer_overflow = 0;
        
        /* 增加错误计数 */
        v831_error_count++;
        
        /* 如果错误过多，尝试重新初始化 */
        if (v831_error_count >= V831_MAX_ERRORS)
        {
            v831_init();
        }
    }
}

/**
 * @brief 获取当前缺陷信息
 * @param 无
 * @retval 缺陷信息结构体
 */
defect_info_t v831_get_defect_info(void)
{
    return current_defect;
}

/**
 * @brief 判断是否为需要停止的关键缺陷
 * @param defect_type 缺陷类型
 * @retval 1-需要停止处理，0-不需要停止
 */
uint8_t v831_is_critical_defect(uint8_t defect_type)
{
    /* 只有压伤和损伤需要停止处理 */
    if (defect_type == DEFECT_TYPE_YASHANG || defect_type == DEFECT_TYPE_SUNSHANG)
    {
        return 1;  /* 关键缺陷，需要停止 */
    }

    return 0;  /* 非关键缺陷，不需要停止 */
}

