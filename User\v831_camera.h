#ifndef __V831_CAMERA_H
#define __V831_CAMERA_H

#include "sys.h"
#include "pinconfig.h"  


#define DEFECT_THRESHOLD      80    


typedef struct
{
    uint8_t detected;         /* 是否检测到缺陷 */
    uint8_t type;             /* 缺陷类型 */
    uint8_t confidence;       /* 置信度，0-100 */
    uint16_t position;        /* 缺陷位置，单位：毫米 */
} defect_info_t;

#define DEFECT_TYPE_NONE       0   /* 无缺陷 */
#define DEFECT_TYPE_YASHANG    1   /* 压伤 - 需要停止处理 */
#define DEFECT_TYPE_SUNSHANG   2   /* 损伤 - 需要停止处理 */
#define DEFECT_TYPE_OTHER      3   /* 其他类型缺陷 - 不需要停止 */


void v831_init(void);                         /* 初始化V831摄像头模块 */
void v831_update(void);                       /* 更新V831状态，处理接收到的数据 */
defect_info_t v831_get_defect_info(void);     /* 获取当前缺陷信息 */
uint8_t v831_is_critical_defect(uint8_t defect_type); /* 判断是否为需要停止的关键缺陷 */
void v831_rx_callback(uint8_t data);          /* UART接收回调函数 */

#endif /* __V831_CAMERA_H */ 

