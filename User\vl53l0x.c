#include "sys.h"
#include "delay.h"
#include <stdio.h>
#include <stdlib.h>
#include "vl53l0x.h"


static uint16_t g_current_distance = 0;

static distance_status_t g_distance_status = DISTANCE_STATUS_INVALID;


/* 使用外部HAL_GetTick函数 */
extern uint32_t HAL_GetTick(void);

static uint32_t get_system_ms(void)
{
    return HAL_GetTick();
}

/* VL53L0X寄存器定义 */
#define VL53L0X_REG_IDENTIFICATION_MODEL_ID       0xC0
#define VL53L0X_REG_IDENTIFICATION_REVISION_ID    0xC2
#define VL53L0X_REG_SYSRANGE_START                0x00
#define VL53L0X_REG_RESULT_RANGE_STATUS           0x14
#define VL53L0X_REG_RESULT_INTERRUPT_STATUS       0x13
#define VL53L0X_REG_RESULT_RANGE_VAL              0x1E

/* I2C通信超时时间 */
#define I2C_TIMEOUT                               1000

/* I2C通信状态 */
static uint8_t g_i2c_initialized = 0;

/* I2C延时函数 */
static void i2c_delay(void)
{
    delay_us(2);  /* 2微秒延时，适用于100KHz I2C */
}

/* I2C起始信号 */
static void i2c_start(void)
{
    /* 确保SDA和SCL都为高 */
    sys_gpio_pin_set(VL53L0X_SDA_GPIO, VL53L0X_SDA_PIN, 1);
    sys_gpio_pin_set(VL53L0X_SCL_GPIO, VL53L0X_SCL_PIN, 1);
    i2c_delay();
    
    /* SDA从高变低，产生起始信号 */
    sys_gpio_pin_set(VL53L0X_SDA_GPIO, VL53L0X_SDA_PIN, 0);
    i2c_delay();
    
    /* SCL拉低，准备传输数据 */
    sys_gpio_pin_set(VL53L0X_SCL_GPIO, VL53L0X_SCL_PIN, 0);
    i2c_delay();
}

/* I2C停止信号 */
static void i2c_stop(void)
{
    /* 确保SDA为低，SCL为低 */
    sys_gpio_pin_set(VL53L0X_SDA_GPIO, VL53L0X_SDA_PIN, 0);
    sys_gpio_pin_set(VL53L0X_SCL_GPIO, VL53L0X_SCL_PIN, 0);
    i2c_delay();
    
    /* SCL拉高 */
    sys_gpio_pin_set(VL53L0X_SCL_GPIO, VL53L0X_SCL_PIN, 1);
    i2c_delay();
    
    /* SDA从低变高，产生停止信号 */
    sys_gpio_pin_set(VL53L0X_SDA_GPIO, VL53L0X_SDA_PIN, 1);
    i2c_delay();
}

/* I2C等待应答 */
static uint8_t i2c_wait_ack(void)
{
    uint8_t ack;
    uint16_t timeout = 0;
    
    /* 设置SDA为输入 */
    sys_gpio_set(VL53L0X_SDA_GPIO, VL53L0X_SDA_PIN, 
                 SYS_GPIO_MODE_IN, SYS_GPIO_OTYPE_OD, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);
    
    /* SCL拉高 */
    sys_gpio_pin_set(VL53L0X_SCL_GPIO, VL53L0X_SCL_PIN, 1);
    i2c_delay();
    
    /* 等待SDA变低（应答信号） */
    while (sys_gpio_pin_get(VL53L0X_SDA_GPIO, VL53L0X_SDA_PIN))
    {
        timeout++;
        if (timeout > I2C_TIMEOUT)
        {
            /* 超时，无应答 */
            i2c_stop();
            return 1;
        }
    }
    
    /* 收到应答 */
    ack = 0;
    
    /* SCL拉低 */
    sys_gpio_pin_set(VL53L0X_SCL_GPIO, VL53L0X_SCL_PIN, 0);
    
    /* 恢复SDA为输出 */
    sys_gpio_set(VL53L0X_SDA_GPIO, VL53L0X_SDA_PIN, 
                 SYS_GPIO_MODE_OUT, SYS_GPIO_OTYPE_OD, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);
    
    return ack;
}

/* I2C产生应答 */
static void i2c_ack(void)
{
    /* SCL拉低 */
    sys_gpio_pin_set(VL53L0X_SCL_GPIO, VL53L0X_SCL_PIN, 0);
    
    /* SDA拉低，产生应答信号 */
    sys_gpio_pin_set(VL53L0X_SDA_GPIO, VL53L0X_SDA_PIN, 0);
    i2c_delay();
    
    /* SCL拉高 */
    sys_gpio_pin_set(VL53L0X_SCL_GPIO, VL53L0X_SCL_PIN, 1);
    i2c_delay();
    
    /* SCL拉低 */
    sys_gpio_pin_set(VL53L0X_SCL_GPIO, VL53L0X_SCL_PIN, 0);
}

/* I2C不产生应答 */
static void i2c_nack(void)
{
    /* SCL拉低 */
    sys_gpio_pin_set(VL53L0X_SCL_GPIO, VL53L0X_SCL_PIN, 0);
    
    /* SDA拉高，不产生应答信号 */
    sys_gpio_pin_set(VL53L0X_SDA_GPIO, VL53L0X_SDA_PIN, 1);
    i2c_delay();
    
    /* SCL拉高 */
    sys_gpio_pin_set(VL53L0X_SCL_GPIO, VL53L0X_SCL_PIN, 1);
    i2c_delay();
    
    /* SCL拉低 */
    sys_gpio_pin_set(VL53L0X_SCL_GPIO, VL53L0X_SCL_PIN, 0);
}

/* I2C发送一个字节 */
static void i2c_send_byte(uint8_t data)
{
    uint8_t i;
    
    /* 依次发送8位数据，从高位开始 */
    for (i = 0; i < 8; i++)
    {
        /* SCL拉低，准备设置SDA */
        sys_gpio_pin_set(VL53L0X_SCL_GPIO, VL53L0X_SCL_PIN, 0);
        
        /* 设置SDA */
        if (data & 0x80)
        {
            sys_gpio_pin_set(VL53L0X_SDA_GPIO, VL53L0X_SDA_PIN, 1);
        }
        else
        {
            sys_gpio_pin_set(VL53L0X_SDA_GPIO, VL53L0X_SDA_PIN, 0);
        }
        
        /* 数据左移一位，准备发送下一位 */
        data <<= 1;
        i2c_delay();
        
        /* SCL拉高，数据有效 */
        sys_gpio_pin_set(VL53L0X_SCL_GPIO, VL53L0X_SCL_PIN, 1);
        i2c_delay();
    }
    
    /* SCL拉低，结束发送 */
    sys_gpio_pin_set(VL53L0X_SCL_GPIO, VL53L0X_SCL_PIN, 0);
}

/* I2C接收一个字节 */
static uint8_t i2c_read_byte(uint8_t ack)
{
    uint8_t i;
    uint8_t data = 0;
    
    /* 设置SDA为输入 */
    sys_gpio_set(VL53L0X_SDA_GPIO, VL53L0X_SDA_PIN, 
                 SYS_GPIO_MODE_IN, SYS_GPIO_OTYPE_OD, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);
    
    /* 依次接收8位数据 */
    for (i = 0; i < 8; i++)
    {
        /* 数据左移一位，为接收新的一位做准备 */
        data <<= 1;
        
        /* SCL拉低 */
        sys_gpio_pin_set(VL53L0X_SCL_GPIO, VL53L0X_SCL_PIN, 0);
        i2c_delay();
        
        /* SCL拉高，读取SDA */
        sys_gpio_pin_set(VL53L0X_SCL_GPIO, VL53L0X_SCL_PIN, 1);
        i2c_delay();
        
        /* 读取SDA状态 */
        if (sys_gpio_pin_get(VL53L0X_SDA_GPIO, VL53L0X_SDA_PIN))
        {
            data |= 0x01;
        }
    }
    
    /* SCL拉低 */
    sys_gpio_pin_set(VL53L0X_SCL_GPIO, VL53L0X_SCL_PIN, 0);
    
    /* 恢复SDA为输出 */
    sys_gpio_set(VL53L0X_SDA_GPIO, VL53L0X_SDA_PIN, 
                 SYS_GPIO_MODE_OUT, SYS_GPIO_OTYPE_OD, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);
    
    /* 根据ack参数决定是否发送应答 */
    if (ack)
    {
        i2c_ack();  /* 发送应答 */
    }
    else
    {
        i2c_nack();  /* 不发送应答 */
    }
    
    return data;
}

/* 写入一个字节到VL53L0X寄存器 */
static uint8_t vl53l0x_write_byte(uint8_t reg, uint8_t data)
{
    i2c_start();
    
    /* 发送设备地址+写命令 */
    i2c_send_byte((VL53L0X_ADDR << 1) | 0x00);
    if (i2c_wait_ack())
    {
        i2c_stop();
        return 1;  /* 无应答 */
    }
    
    /* 发送寄存器地址 */
    i2c_send_byte(reg);
    if (i2c_wait_ack())
    {
        i2c_stop();
        return 1;  /* 无应答 */
    }
    
    /* 发送数据 */
    i2c_send_byte(data);
    if (i2c_wait_ack())
    {
        i2c_stop();
        return 1;  /* 无应答 */
    }
    
    i2c_stop();
    return 0;  /* 成功 */
}

/* 从VL53L0X寄存器读取一个字节 */
static uint8_t vl53l0x_read_byte(uint8_t reg, uint8_t *data)
{
    i2c_start();
    
    /* 发送设备地址+写命令 */
    i2c_send_byte((VL53L0X_ADDR << 1) | 0x00);
    if (i2c_wait_ack())
    {
        i2c_stop();
        return 1;  /* 无应答 */
    }
    
    /* 发送寄存器地址 */
    i2c_send_byte(reg);
    if (i2c_wait_ack())
    {
        i2c_stop();
        return 1;  /* 无应答 */
    }
    
    /* 重新开始，准备读取数据 */
    i2c_start();
    
    /* 发送设备地址+读命令 */
    i2c_send_byte((VL53L0X_ADDR << 1) | 0x01);
    if (i2c_wait_ack())
    {
        i2c_stop();
        return 1;  /* 无应答 */
    }
    
    /* 读取数据 */
    *data = i2c_read_byte(0);  /* 读取后不发送应答 */
    
    i2c_stop();
    return 0;  /* 成功 */
}

/* 从VL53L0X寄存器读取多个字节 */
static uint8_t vl53l0x_read_multi(uint8_t reg, uint8_t *data, uint8_t count)
{
    uint8_t i;
    
    i2c_start();
    
    /* 发送设备地址+写命令 */
    i2c_send_byte((VL53L0X_ADDR << 1) | 0x00);
    if (i2c_wait_ack())
    {
        i2c_stop();
        return 1;  /* 无应答 */
    }
    
    /* 发送寄存器地址 */
    i2c_send_byte(reg);
    if (i2c_wait_ack())
    {
        i2c_stop();
        return 1;  /* 无应答 */
    }
    
    /* 重新开始，准备读取数据 */
    i2c_start();
    
    /* 发送设备地址+读命令 */
    i2c_send_byte((VL53L0X_ADDR << 1) | 0x01);
    if (i2c_wait_ack())
    {
        i2c_stop();
        return 1;  /* 无应答 */
    }
    
    /* 读取多个字节 */
    for (i = 0; i < count - 1; i++)
    {
        data[i] = i2c_read_byte(1);  /* 读取后发送应答 */
    }
    
    /* 最后一个字节读取后不发送应答 */
    data[i] = i2c_read_byte(0);
    
    i2c_stop();
    return 0;  /* 成功 */
}

static void vl53l0x_i2c_init(void)
{
    /* 使能GPIOD时钟 - 更新为使用PD2/PD3引脚 */
    RCC->AHB4ENR |= 1 << 3;    /* GPIOD时钟使能 */

    /* 配置SCL引脚 (PD2) */
    sys_gpio_set(VL53L0X_SCL_GPIO, VL53L0X_SCL_PIN,
                 SYS_GPIO_MODE_OUT, SYS_GPIO_OTYPE_OD, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);

    /* 配置SDA引脚 (PD3) */
    sys_gpio_set(VL53L0X_SDA_GPIO, VL53L0X_SDA_PIN,
                 SYS_GPIO_MODE_OUT, SYS_GPIO_OTYPE_OD, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);
    
    
    sys_gpio_pin_set(VL53L0X_SCL_GPIO, VL53L0X_SCL_PIN, 1);
    sys_gpio_pin_set(VL53L0X_SDA_GPIO, VL53L0X_SDA_PIN, 1);
    
    
    delay_ms(10);
    
    /* 标记I2C已初始化 */
    g_i2c_initialized = 1;
}

/* 检查VL53L0X是否存在 */
static uint8_t vl53l0x_check_id(void)
{
    uint8_t id = 0;
    
    /* 读取设备ID */
    if (vl53l0x_read_byte(VL53L0X_REG_IDENTIFICATION_MODEL_ID, &id))
    {
        return 0;  /* 读取失败 */
    }
    
    /* 检查ID是否正确 (0xEE for VL53L0X) */
    if (id == 0xEE)
    {
        return 1;  /* ID正确 */
    }
    
    return 0;  /* ID不正确 */
}

/* 启动单次测距 */
static void vl53l0x_start_ranging(void)
{
    /* 写入0x01到SYSRANGE_START寄存器，启动单次测距 */
    vl53l0x_write_byte(VL53L0X_REG_SYSRANGE_START, 0x01);
}

/* 检查测距是否完成 */
static uint8_t vl53l0x_poll_ranging(void)
{
    uint8_t status = 0;
    
    /* 读取中断状态寄存器 */
    vl53l0x_read_byte(VL53L0X_REG_RESULT_INTERRUPT_STATUS, &status);
    
    /* 检查第0位是否为1，表示新的测量结果可用 */
    return (status & 0x07) ? 1 : 0;
}

/* 读取测距结果 */
static uint16_t vl53l0x_read_range_mm(void)
{
    uint8_t data[2];
    uint16_t range = 0;
    
    /* 读取测距结果寄存器 */
    vl53l0x_read_multi(VL53L0X_REG_RESULT_RANGE_VAL, data, 2);
    
    /* 清除中断标志 */
    vl53l0x_write_byte(VL53L0X_REG_RESULT_INTERRUPT_STATUS, 0x01);
    
    /* 计算距离值（毫米） */
    range = ((uint16_t)data[0] << 8) | data[1];
    
    return range;
}


void vl53l0x_init(void)
{
   
    vl53l0x_i2c_init();
    
   
    delay_ms(50);  
    
   
    g_current_distance = 0;
    g_distance_status = DISTANCE_STATUS_INVALID;
    
    /* 检查传感器是否存在 */
    if (!vl53l0x_check_id())
    {
        /* 传感器不存在或通信失败 */
        g_distance_status = DISTANCE_STATUS_INVALID;
        return;
    }
    
    /* 初始化完成后读取一次距离 */
    vl53l0x_read_distance();
}


static void update_distance_status(uint16_t distance)
{
   
    g_current_distance = distance;
    
    
    if (distance >= DISTANCE_MAX)
    {
      
        g_distance_status = DISTANCE_STATUS_INVALID;
    }
    else if (distance <= DISTANCE_TOO_CLOSE)
    {
       
        g_distance_status = DISTANCE_STATUS_TOO_CLOSE;
    }
    else if (distance >= DISTANCE_TOO_FAR)
    {
        
        g_distance_status = DISTANCE_STATUS_TOO_FAR;
    }
    else
    {
      
        g_distance_status = DISTANCE_STATUS_GOOD;
    }
}


uint16_t vl53l0x_read_distance(void)
{
    uint16_t distance = 0;
    uint8_t timeout_count = 0;
    static uint32_t last_read_time = 0;
    uint32_t current_time = get_system_ms();
    
    /* 限制读取频率，避免过于频繁的I2C通信 */
    if (current_time - last_read_time < 50)
    {
        return g_current_distance;
    }
    
    last_read_time = current_time;
   
    /* 检查I2C是否已初始化 */
    if (!g_i2c_initialized)
    {
        vl53l0x_i2c_init();
    }
    
    /* 启动单次测距 */
    vl53l0x_start_ranging();
    
    /* 等待测距完成，最多等待100ms */
    while (!vl53l0x_poll_ranging())
    {
        delay_ms(1);
        timeout_count++;
        
        if (timeout_count > 100)
        {
            /* 超时，返回无效距离 */
            distance = DISTANCE_MAX;
            update_distance_status(distance);
            return distance;
        }
    }
    
    /* 读取测距结果 */
    distance = vl53l0x_read_range_mm();
    
    /* 更新距离状态 */
    update_distance_status(distance);
    
    return distance;
}


uint8_t vl53l0x_check_clamp(void)
{
    
    vl53l0x_read_distance();
    
    
    if (g_distance_status == DISTANCE_STATUS_GOOD)
    {
        return 1;
    }
    else
    {
        return 0;
    }
}


distance_status_t vl53l0x_get_status(void)
{
    return g_distance_status;
} 

