#ifndef __VL53L0X_H
#define __VL53L0X_H

#include "sys.h"


/* 使用pinconfig.h中的统一配置 */
#include "pinconfig.h"

/* VL53L0X使用与OLED相同的I2C1总线 */
#define VL53L0X_SCL_GPIO       OLED_I2C_SCL_GPIO_PORT
#define VL53L0X_SCL_PIN        SYS_GPIO_PIN6  /* PB6 */
#define VL53L0X_SDA_GPIO       OLED_I2C_SDA_GPIO_PORT
#define VL53L0X_SDA_PIN        SYS_GPIO_PIN5  /* PB5 */


#define VL53L0X_ADDR           0x29  


#define DISTANCE_THRESHOLD     50     
#define DISTANCE_TOO_CLOSE     30     
#define DISTANCE_TOO_FAR       80     
#define DISTANCE_MAX           2000   


typedef enum {
    DISTANCE_STATUS_GOOD = 0,  
    DISTANCE_STATUS_TOO_CLOSE, 
    DISTANCE_STATUS_TOO_FAR,   
    DISTANCE_STATUS_INVALID    
} distance_status_t;


void vl53l0x_init(void);                 
uint16_t vl53l0x_read_distance(void);    
uint8_t vl53l0x_check_clamp(void);       
distance_status_t vl53l0x_get_status(void); 

#endif /* __VL53L0X_H */ 

